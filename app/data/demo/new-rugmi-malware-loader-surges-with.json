{"text": "A new malware loader is being used by threat actors to deliver a wide range of information stealers such as <PERSON><PERSON> Stealer (aka LummaC2), Vidar, RecordBreaker (aka Raccoon Stealer V2), and Rescoms. Cybersecurity firm ESET is tracking the trojan under the name Win/TrojanDownloader.Rugmi. This malware is a loader with three types of components: a downloader that downloads an encrypted payload, a loader that runs the payload from internal resources, and another loader that runs the payload from an external file on the disk. Telemetry data gathered by the company shows that detections for the Rugmi loader spiked in October and November 2023, surging from single digit daily numbers to hundreds per day. Stealer malware is typically sold under a malware-as-a-service (MaaS) model to other threat actors on a subscription basis. Lumma Stealer, for instance, is advertised in underground forums for $250 a month. The most expensive plan costs $20,000, but it also gives the customers access to the source code and the right to sell it.", "explicit_triplets": [{"subject": "threat actors", "relation": "use", "object": "Win/TrojanDownloader.Rugmi"}, {"subject": "Win/TrojanDownloader.Rugmi", "relation": "delivers", "object": "<PERSON><PERSON>"}, {"subject": "Win/TrojanDownloader.Rugmi", "relation": "delivers", "object": "V<PERSON>r"}, {"subject": "Win/TrojanDownloader.Rugmi", "relation": "delivers", "object": "RecordBreaker"}, {"subject": "Win/TrojanDownloader.Rugmi", "relation": "delivers", "object": "Rescoms"}, {"subject": "<PERSON><PERSON>", "relation": "is also known as", "object": "LummaC2"}, {"subject": "RecordBreaker", "relation": "is also known as", "object": "<PERSON><PERSON><PERSON> Stealer V2"}, {"subject": "Win/TrojanDownloader.Rugmi", "relation": "has component", "object": "a downloader that downloads an encrypted payload"}, {"subject": "Win/TrojanDownloader.Rugmi", "relation": "has component", "object": "a loader that runs the payload from internal resources"}, {"subject": "Win/TrojanDownloader.Rugmi", "relation": "has component", "object": "a loader that runs the payload from an external file on the disk"}, {"subject": "Win/TrojanDownloader.Rugmi", "relation": "spiked detections in", "object": "October and November 2023"}, {"subject": "<PERSON><PERSON>", "relation": "is advertised in", "object": "underground forums"}, {"subject": "<PERSON><PERSON>", "relation": "costs subscription", "object": "$250 a month"}, {"subject": "<PERSON><PERSON>", "relation": "most expensive plan costs", "object": "$20,000"}, {"subject": "<PERSON><PERSON>", "relation": "most expensive plan grants", "object": "access to the source code"}], "entities": [{"entity_id": 0, "entity_name": "threat actors", "entity_type": "Attacker", "mentions": []}, {"entity_id": 1, "entity_name": "Win/TrojanDownloader.Rugmi", "entity_type": "Malware", "mentions": []}, {"entity_id": 2, "entity_name": "<PERSON><PERSON>", "entity_type": "Malware", "mentions": []}, {"entity_id": 3, "entity_name": "V<PERSON>r", "entity_type": "Malware", "mentions": []}, {"entity_id": 4, "entity_name": "RecordBreaker", "entity_type": "Malware", "mentions": []}, {"entity_id": 5, "entity_name": "Rescoms", "entity_type": "Malware", "mentions": []}, {"entity_id": 6, "entity_name": "LummaC2", "entity_type": "Malware", "mentions": []}, {"entity_id": 7, "entity_name": "<PERSON><PERSON><PERSON> Stealer V2", "entity_type": "Malware", "mentions": []}, {"entity_id": 8, "entity_name": "a downloader that downloads an encrypted payload", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 9, "entity_name": "a loader that runs the payload from internal resources", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 15, "entity_name": "a loader that runs the payload from an external file on the disk", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 10, "entity_name": "October and November 2023", "entity_type": "Time", "mentions": []}, {"entity_id": 11, "entity_name": "underground forums", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 12, "entity_name": "$250 a month", "entity_type": "Information", "mentions": []}, {"entity_id": 13, "entity_name": "$20,000", "entity_type": "Information", "mentions": []}, {"entity_id": 14, "entity_name": "access to the source code", "entity_type": "Malware Characteristic:Capability", "mentions": []}], "implicit_triplets": []}