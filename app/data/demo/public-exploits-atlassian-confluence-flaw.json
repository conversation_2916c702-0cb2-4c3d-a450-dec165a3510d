{"text": "Threat actors are using public exploits to pummel a critical zero-day remote code execution (RCE) flaw that affects all versions of a popular collaboration tool used in cloud and hybrid server environments and allows for complete host takeover.Researchers from Volexity uncovered the flaw in Atlassian Confluence Server and Data Center software over the Memorial Day weekend after they detected suspicious activity on two internet-facing web servers belonging to a customer running the software, they said in a blog post published last week. The researchers tracked the activity to a public exploit for the vulnerability, CVE-2022-26134, that’s been spreading rapidly, and subsequently reported the flaw to Atlassian. As observed by Volexity researchers, what’s being described as an “OGNL injection vulnerability” appears to allow for a Java Server Page (JSP) webshell to be written into a publicly accessible web directory on Confluence software.", "explicit_triplets": [{"subject": "Threat actors", "relation": "are using", "object": "public exploits"}, {"subject": "public exploits", "relation": "target", "object": "a critical zero-day remote code execution (RCE) flaw"}, {"subject": "a critical zero-day remote code execution (RCE) flaw", "relation": "affects", "object": "Atlassian Confluence Server and Data Center software"}, {"subject": "Researchers from Volexity", "relation": "uncovered", "object": "the flaw in Atlassian Confluence Server and Data Center software"}, {"subject": "Researchers from Volexity", "relation": "detected", "object": "suspicious activity"}, {"subject": "suspicious activity", "relation": "was detected on", "object": "two internet-facing web servers"}, {"subject": "two internet-facing web servers", "relation": "belonged to", "object": "a customer"}, {"subject": "Volexity researchers", "relation": "tracked", "object": "activity to a public exploit for CVE-2022-26134"}, {"subject": "Volexity researchers", "relation": "reported", "object": "the flaw to Atlassian"}, {"subject": "CVE-2022-26134", "relation": "described as", "object": "an OGNL injection vulnerability"}, {"subject": "an OGNL injection vulnerability", "relation": "allows writing", "object": "a Java Server Page (JSP) webshell"}, {"subject": "a Java Server Page (JSP) webshell", "relation": "written into", "object": "a publicly accessible web directory on Confluence software"}], "entities": [{"entity_id": 0, "entity_name": "Threat actors", "entity_type": "Attacker", "mentions": []}, {"entity_id": 1, "entity_name": "public exploits", "entity_type": "Exploit Target", "mentions": []}, {"entity_id": 2, "entity_name": "a critical zero-day remote code execution (RCE) flaw", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 3, "entity_name": "Atlassian Confluence Server and Data Center software", "entity_type": "Infrastructure", "mentions": ["a publicly accessible web directory on Confluence software"]}, {"entity_id": 4, "entity_name": "Researchers from Volexity", "entity_type": "Organization", "mentions": ["Volexity researchers"]}, {"entity_id": 6, "entity_name": "suspicious activity", "entity_type": "Event", "mentions": []}, {"entity_id": 7, "entity_name": "two internet-facing web servers", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 8, "entity_name": "a customer", "entity_type": "Organization", "mentions": []}, {"entity_id": 9, "entity_name": "activity to a public exploit for CVE-2022-26134", "entity_type": "Exploit Target", "mentions": []}, {"entity_id": 5, "entity_name": "the flaw to Atlassian", "entity_type": "Vulnerability", "mentions": ["the flaw in Atlassian Confluence Server and Data Center software"]}, {"entity_id": 10, "entity_name": "CVE-2022-26134", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 11, "entity_name": "an OGNL injection vulnerability", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 12, "entity_name": "a Java Server Page (JSP) webshell", "entity_type": "Malware Characteristic:Payload", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 4, "entity_text": "Researchers from Volexity"}, "relation": "discovered", "object": {"entity_id": 11, "entity_text": "an OGNL injection vulnerability"}}]}