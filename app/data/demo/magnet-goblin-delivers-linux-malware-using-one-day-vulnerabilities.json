{"text": "A financially motivated threat actor has been targeting one-day vulnerabilities in public-facing services to deploy Linux backdoors, Check Point reports. Tracked as <PERSON><PERSON><PERSON>, the adversary was seen quickly adopting one-day vulnerabilities, often in edge devices, and relying on the Nerbian custom malware family to perform nefarious activities. <PERSON><PERSON><PERSON> was seen targeting publicly disclosed vulnerabilities in Ivanti VPNs (CVE-2023-46805, CVE-2024-21887, CVE-2024-21888, and CVE-2024-21893), Magento (CVE-2022-24086), Qlik Sense (CVE-2023-41265, CVE-2023-41266, and CVE-2023-48365), and possibly Apache ActiveMQ. As part of an attack exploiting the recent Ivanti flaws, the threat actor was observed deploying a JavaScript credential stealer called Warpwire, a Linux variant of the NerbianRAT backdoor, and the open source tunneling tool Ligolo. The Warpwire stealer was previously linked to the mass exploitation of Ivanti vulnerabilities, suggesting that multiple threat actors might be using it, Check Point says. The tool was also seen in a 2022 attack against Magento servers, which were used as command-and-control (C&C) servers for the Windows variant of NerbianRAT and for Warpwire. <PERSON><PERSON><PERSON> deployed MiniNerbian, a smaller version of the Linux NerbianRAT backdoor, on the compromised Magento instances.", "explicit_triplets": [{"subject": "Threat Actor", "relation": "targets", "object": "one-day vulnerabilities"}, {"subject": "Threat Actor", "relation": "deploys", "object": "Linux backdoors"}, {"subject": "Threat Actor", "relation": "tracked as", "object": "<PERSON><PERSON><PERSON>"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "adopts", "object": "one-day vulnerabilities"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "relies on", "object": "Nerbian custom malware family"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "targets", "object": "Ivanti VP<PERSON>s"}, {"subject": "Ivanti VP<PERSON>s", "relation": "have vulnerabilities", "object": "CVE-2023-46805"}, {"subject": "Ivanti VP<PERSON>s", "relation": "have vulnerabilities", "object": "CVE-2024-21887"}, {"subject": "Ivanti VP<PERSON>s", "relation": "have vulnerabilities", "object": "CVE-2024-21888"}, {"subject": "Ivanti VP<PERSON>s", "relation": "have vulnerabilities", "object": "CVE-2024-21893"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "targets", "object": "Magento"}, {"subject": "Magento", "relation": "has vulnerability", "object": "CVE-2022-24086"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "targets", "object": "<PERSON><PERSON>"}, {"subject": "<PERSON><PERSON>", "relation": "has vulnerability", "object": "CVE-2023-41265"}, {"subject": "<PERSON><PERSON>", "relation": "has vulnerability", "object": "CVE-2023-41266"}, {"subject": "<PERSON><PERSON>", "relation": "has vulnerability", "object": "CVE-2023-48365"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "targets", "object": "Apache ActiveMQ"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "deploys", "object": "Warpwire"}, {"subject": "Warpwire", "relation": "is a", "object": "Indicator"}, {"subject": "Warpwire", "relation": "steals", "object": "credentials"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "deploys", "object": "NerbianRAT"}, {"subject": "NerbianRAT", "relation": "is a variant of", "object": "Nerbian custom malware family"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "deploys", "object": "Ligolo"}, {"subject": "Warpwire", "relation": "linked to", "object": "mass exploitation of Ivanti vulnerabilities"}, {"subject": "Warpwire", "relation": "used in", "object": "2022 attack against Magento servers"}, {"subject": "Magento servers", "relation": "used as", "object": "command-and-control (C&C) servers"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "deployed", "object": "MiniNerbian"}, {"subject": "MiniNerbian", "relation": "is a variant of", "object": "NerbianRAT"}, {"subject": "MiniNerbian", "relation": "deployed on", "object": "Magento instances"}], "entities": [{"entity_id": 0, "entity_name": "Threat Actor", "entity_type": "Attacker", "mentions": []}, {"entity_id": 1, "entity_name": "one-day vulnerabilities", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 2, "entity_name": "Linux backdoors", "entity_type": "Malware", "mentions": []}, {"entity_id": 3, "entity_name": "<PERSON><PERSON><PERSON>", "entity_type": "Attacker", "mentions": []}, {"entity_id": 5, "entity_name": "Ivanti VP<PERSON>s", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 6, "entity_name": "CVE-2023-46805", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 7, "entity_name": "CVE-2024-21887", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 8, "entity_name": "CVE-2024-21888", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 9, "entity_name": "CVE-2024-21893", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 10, "entity_name": "Magento", "entity_type": "Infrastructure", "mentions": ["Magento servers", "Magento instances"]}, {"entity_id": 11, "entity_name": "CVE-2022-24086", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 12, "entity_name": "<PERSON><PERSON>", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 13, "entity_name": "CVE-2023-41265", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 14, "entity_name": "CVE-2023-41266", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 15, "entity_name": "CVE-2023-48365", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 16, "entity_name": "Apache ActiveMQ", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 17, "entity_name": "Warpwire", "entity_type": "Malware", "mentions": []}, {"entity_id": 18, "entity_name": "Indicator", "entity_type": "Indicator", "mentions": []}, {"entity_id": 19, "entity_name": "credentials", "entity_type": "Credential", "mentions": []}, {"entity_id": 4, "entity_name": "NerbianRAT", "entity_type": "Malware", "mentions": ["Nerbian custom malware family"]}, {"entity_id": 20, "entity_name": "Ligolo", "entity_type": "Malware", "mentions": []}, {"entity_id": 21, "entity_name": "mass exploitation of Ivanti vulnerabilities", "entity_type": "Event", "mentions": []}, {"entity_id": 22, "entity_name": "2022 attack against Magento servers", "entity_type": "Event", "mentions": []}, {"entity_id": 23, "entity_name": "command-and-control (C&C) servers", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 24, "entity_name": "MiniNerbian", "entity_type": "Malware", "mentions": []}], "implicit_triplets": []}