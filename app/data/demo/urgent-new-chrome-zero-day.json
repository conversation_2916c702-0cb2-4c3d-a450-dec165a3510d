{"text": "Google has rolled out security updates for the Chrome web browser to address a high-severity zero-day flaw that it said has been exploited in the wild. The vulnerability, assigned the CVE identifier CVE-2023-7024, has been described as a heap-based buffer overflow bug in the WebRTC framework that could be exploited to result in program crashes or arbitrary code execution. <PERSON><PERSON><PERSON> and <PERSON> of Google's Threat Analysis Group (TAG) have been credited with discovering and reporting the flaw on December 19, 2023. No other details about the security defect have been released to prevent further abuse, with Google acknowledging that an exploit for CVE-2023-7024 exists in the wild. Given that WebRTC is an open-source project and that it's also supported by Mozilla Firefox and Apple Safari, it's currently not clear if the flaw has any impact beyond Chrome and Chromium-based browsers.", "explicit_triplets": [{"subject": "Google", "relation": "rolled out security updates for", "object": "the Chrome web browser"}, {"subject": "Chrome web browser", "relation": "addresses", "object": "high-severity zero-day flaw"}, {"subject": "high-severity zero-day flaw", "relation": "is assigned", "object": "CVE-2023-7024"}, {"subject": "CVE-2023-7024", "relation": "described as", "object": "heap-based buffer overflow bug"}, {"subject": "heap-based buffer overflow bug", "relation": "located in", "object": "WebRTC framework"}, {"subject": "heap-based buffer overflow bug", "relation": "could be exploited to result in", "object": "arbitrary code execution"}, {"subject": "heap-based buffer overflow bug", "relation": "could be exploited to result in", "object": "program crashes"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "discovered and reported", "object": "CVE-2023-7024"}, {"subject": "<PERSON>", "relation": "discovered and reported", "object": "CVE-2023-7024"}, {"subject": "Google", "relation": "acknowledges", "object": "exploit for CVE-2023-7024 exists in the wild"}, {"subject": "WebRTC", "relation": "is supported by", "object": "Mozilla Firefox"}, {"subject": "WebRTC", "relation": "is supported by", "object": "Apple Safari"}], "entities": [{"entity_id": 0, "entity_name": "Google", "entity_type": "Organization", "mentions": []}, {"entity_id": 1, "entity_name": "Chrome web browser", "entity_type": "Tool", "mentions": ["the Chrome web browser"]}, {"entity_id": 2, "entity_name": "high-severity zero-day flaw", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 3, "entity_name": "CVE-2023-7024", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 4, "entity_name": "heap-based buffer overflow bug", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 6, "entity_name": "arbitrary code execution", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 7, "entity_name": "program crashes", "entity_type": "Malware Characteristic:Behavior", "mentions": []}, {"entity_id": 8, "entity_name": "<PERSON><PERSON><PERSON>", "entity_type": "Attacker", "mentions": []}, {"entity_id": 9, "entity_name": "<PERSON>", "entity_type": "Attacker", "mentions": []}, {"entity_id": 10, "entity_name": "exploit for CVE-2023-7024 exists in the wild", "entity_type": "Exploit Target", "mentions": []}, {"entity_id": 5, "entity_name": "WebRTC", "entity_type": "Infrastructure", "mentions": ["WebRTC framework"]}, {"entity_id": 11, "entity_name": "Mozilla Firefox", "entity_type": "Tool", "mentions": []}, {"entity_id": 12, "entity_name": "Apple Safari", "entity_type": "Tool", "mentions": []}], "implicit_triplets": []}