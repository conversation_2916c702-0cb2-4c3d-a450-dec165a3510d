{"text": "A new Android backdoor has been discovered with potent capabilities to carry out a range of malicious actions on infected devices. Dubbed Xamalicious by the McAfee Mobile Research Team, the malware is so named for the fact that it's developed using an open-source mobile app framework called Xamarin and abuses the operating system's accessibility permissions to fulfill its objectives. It's also capable of gathering metadata about the compromised device and contacting a command-and-control (C2) server to fetch a second-stage payload, but only after determining if it fits the bill. The second stage is dynamically injected as an assembly DLL at runtime level to take full control of the device and potentially perform fraudulent actions such as clicking on ads, installing apps, among other actions financially motivated without user consent. The cybersecurity firm said it identified 25 apps that come with this active threat, some of which were distributed on the official Google Play Store since mid-2020. The apps are estimated to have been installed at least 327,000 times.", "explicit_triplets": [{"subject": "Xamalicious", "relation": "is a type of", "object": "Malware"}, {"subject": "Xamalicious", "relation": "was discovered by", "object": "McAfee Mobile Research Team"}, {"subject": "Xamalicious", "relation": "developed using", "object": "<PERSON><PERSON><PERSON>"}, {"subject": "Xamalicious", "relation": "abuses", "object": "Operating System accessibility permissions"}, {"subject": "Xamalicious", "relation": "is capable of gathering", "object": "metadata about the compromised device"}, {"subject": "Xamalicious", "relation": "contacts", "object": "a command-and-control (C2) server"}, {"subject": "command-and-control (C2) server", "relation": "provides", "object": "second-stage payload"}, {"subject": "second-stage payload", "relation": "is injected as", "object": "assembly DLL"}, {"subject": "assembly DLL", "relation": "performs", "object": "fraudulent actions"}, {"subject": "fraudulent actions", "relation": "include", "object": "clicking on ads"}, {"subject": "fraudulent actions", "relation": "include", "object": "installing apps"}, {"subject": "25 apps", "relation": "contain", "object": "Xamalicious"}, {"subject": "25 apps", "relation": "were distributed via", "object": "Google Play Store"}, {"subject": "25 apps", "relation": "distributed since", "object": "mid-2020"}], "entities": [{"entity_id": 0, "entity_name": "Xamalicious", "entity_type": "Malware", "mentions": []}, {"entity_id": 1, "entity_name": "Malware", "entity_type": "Malware", "mentions": []}, {"entity_id": 2, "entity_name": "McAfee Mobile Research Team", "entity_type": "Organization", "mentions": []}, {"entity_id": 3, "entity_name": "<PERSON><PERSON><PERSON>", "entity_type": "Tool", "mentions": []}, {"entity_id": 4, "entity_name": "Operating System accessibility permissions", "entity_type": "Exploit Target", "mentions": []}, {"entity_id": 5, "entity_name": "metadata about the compromised device", "entity_type": "Information", "mentions": []}, {"entity_id": 6, "entity_name": "command-and-control (C2) server", "entity_type": "Infrastructure", "mentions": ["a command-and-control (C2) server"]}, {"entity_id": 7, "entity_name": "second-stage payload", "entity_type": "Malware Characteristic:Payload", "mentions": []}, {"entity_id": 8, "entity_name": "assembly DLL", "entity_type": "Indicator:File", "mentions": []}, {"entity_id": 9, "entity_name": "fraudulent actions", "entity_type": "Malware Characteristic:Behavior", "mentions": []}, {"entity_id": 10, "entity_name": "clicking on ads", "entity_type": "Malware Characteristic:Behavior", "mentions": []}, {"entity_id": 11, "entity_name": "installing apps", "entity_type": "Malware Characteristic:Behavior", "mentions": []}, {"entity_id": 12, "entity_name": "25 apps", "entity_type": "Indicator:File", "mentions": []}, {"entity_id": 13, "entity_name": "Google Play Store", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 14, "entity_name": "mid-2020", "entity_type": "Time", "mentions": []}], "implicit_triplets": []}