{"text": "Mastodon, the free and open-source decentralized social networking platform, has fixed a critical vulnerability that allows attackers to impersonate and take over any remote account. The newly fixed flaw is tracked as CVE-2024-23832 and stems from insufficient origin validation in Mastodon, allowing attackers to impersonate users and take over their accounts. In July 2023, the Mastodon team fixed another critical bug tracked as CVE-2023-36460 and dubbed 'TootRoot,' which allowed attackers to send 'toots' (the equivalent of tweets) that would create web shells on target instances. Attackers could leverage this flaw to completely compromise Mastodon servers, allowing them to access sensitive user information, communications, and plant backdoors.", "explicit_triplets": [{"subject": "Mastodon", "relation": "fixed", "object": "a critical vulnerability"}, {"subject": "a critical vulnerability", "relation": "allows", "object": "attackers to impersonate and take over any remote account"}, {"subject": "critical vulnerability", "relation": "is tracked as", "object": "CVE-2024-23832"}, {"subject": "CVE-2024-23832", "relation": "stems from", "object": "insufficient origin validation in Mastodon"}, {"subject": "attackers", "relation": "could leverage", "object": "CVE-2024-23832"}, {"subject": "Mastodon team", "relation": "fixed", "object": "critical bug CVE-2023-36460"}, {"subject": "CVE-2023-36460", "relation": "is dubbed", "object": "TootRoot"}, {"subject": "attackers", "relation": "used", "object": "CVE-2023-36460"}, {"subject": "CVE-2023-36460", "relation": "allowed", "object": "attackers to send toots that create web shells"}, {"subject": "attackers", "relation": "could leverage", "object": "CVE-2023-36460"}, {"subject": "CVE-2023-36460", "relation": "allowed", "object": "attackers to completely compromise Mastodon servers"}, {"subject": "attackers", "relation": "could access", "object": "sensitive user information"}, {"subject": "attackers", "relation": "could access", "object": "communications"}, {"subject": "attackers", "relation": "could plant", "object": "backdoors"}], "entities": [{"entity_id": 0, "entity_name": "Mastodon", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 2, "entity_name": "attackers to impersonate and take over any remote account", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 3, "entity_name": "CVE-2024-23832", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 4, "entity_name": "insufficient origin validation in Mastodon", "entity_type": "Malware Characteristic:Behavior", "mentions": []}, {"entity_id": 5, "entity_name": "attackers", "entity_type": "Attacker", "mentions": []}, {"entity_id": 6, "entity_name": "Mastodon team", "entity_type": "Organization", "mentions": []}, {"entity_id": 1, "entity_name": "CVE-2023-36460", "entity_type": "Vulnerability", "mentions": ["critical vulnerability", "critical bug CVE-2023-36460", "a critical vulnerability"]}, {"entity_id": 7, "entity_name": "TootRoot", "entity_type": "Malware", "mentions": []}, {"entity_id": 8, "entity_name": "attackers to send toots that create web shells", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 9, "entity_name": "attackers to completely compromise Mastodon servers", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 10, "entity_name": "sensitive user information", "entity_type": "Information", "mentions": []}, {"entity_id": 11, "entity_name": "communications", "entity_type": "Information", "mentions": []}, {"entity_id": 12, "entity_name": "backdoors", "entity_type": "Malware", "mentions": []}], "implicit_triplets": []}