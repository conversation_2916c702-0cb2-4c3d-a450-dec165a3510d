{"text": "Researchers have added state-sponsored hackers to the list of adversaries attempting to exploit Microsoft’s now-patched Follina vulnerability. According to researchers at Proofpoint, state-sponsored hackers have attempted to abuse the Follina vulnerability in Microsoft Office, aiming an email-based exploit at U.S. and E.U. government targets via phishing campaigns. Proofpoint researchers spotted the attacks and believe the adversaries have ties to a government, which it did not identify. Attacks consist of campaigns targeting victims U.S. and E.U. government workers. Malicious emails contain fake recruitment pitches promising a 20 percent boost in salaries and entice recipients to download an accompanying attachment. The malicious attachment targets the remote code execution bug CVE-2022-30190, dubbed Follina.", "explicit_triplets": [{"subject": "state-sponsored hackers", "relation": "attempt to exploit", "object": "Follina vulnerability"}, {"subject": "Follina vulnerability", "relation": "affects", "object": "Microsoft Office"}, {"subject": "state-sponsored hackers", "relation": "target", "object": "U.S. and E.U. government"}, {"subject": "state-sponsored hackers", "relation": "use", "object": "email-based exploit"}, {"subject": "attacks", "relation": "target", "object": "U.S. and E.U. government workers"}, {"subject": "Malicious emails", "relation": "contain", "object": "fake recruitment pitches"}, {"subject": "Malicious emails", "relation": "entice recipients to download", "object": "attachment"}, {"subject": "malicious attachment", "relation": "targets", "object": "CVE-2022-30190"}], "entities": [{"entity_id": 0, "entity_name": "state-sponsored hackers", "entity_type": "Attacker", "mentions": []}, {"entity_id": 1, "entity_name": "Follina vulnerability", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 2, "entity_name": "Microsoft Office", "entity_type": "Tool", "mentions": []}, {"entity_id": 3, "entity_name": "U.S. and E.U. government", "entity_type": "Organization", "mentions": ["U.S. and E.U. government workers"]}, {"entity_id": 4, "entity_name": "email-based exploit", "entity_type": "Malware", "mentions": []}, {"entity_id": 5, "entity_name": "attacks", "entity_type": "Event", "mentions": []}, {"entity_id": 6, "entity_name": "Malicious emails", "entity_type": "Malware", "mentions": []}, {"entity_id": 7, "entity_name": "fake recruitment pitches", "entity_type": "Information", "mentions": []}, {"entity_id": 8, "entity_name": "attachment", "entity_type": "Indicator:File", "mentions": []}, {"entity_id": 9, "entity_name": "malicious attachment", "entity_type": "Indicator:File", "mentions": []}, {"entity_id": 10, "entity_name": "CVE-2022-30190", "entity_type": "Vulnerability", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 6, "entity_text": "Malicious emails"}, "relation": "are utilized by", "object": {"entity_id": 0, "entity_text": "state-sponsored hackers"}}, {"subject": {"entity_id": 9, "entity_text": "malicious attachment"}, "relation": "is used by", "object": {"entity_id": 0, "entity_text": "state-sponsored hackers"}}]}