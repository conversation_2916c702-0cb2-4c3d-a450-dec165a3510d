{"text": "Maintainers of the Python Package Index (PyPI) repository were forced to suspend new project creation and new user registration on Thursday to mitigate a worrisome malware upload campaign. The confirmation of the PyPI incident, which has since been resolved, comes as security researchers at Checkmarx warn that multiple malicious Python packages are being pushed via typo-squatting techniques. “This is a multi-stage attack and the malicious payload aimed to steal crypto wallets, sensitive data from browsers (cookies, extensions data, etc..) and various credentials. In addition, the malicious payload employed a persistence mechanism to survive reboots,” Checkmarx said in a research note. Earlier this week, the company said it spotted multiple malicious Python packages being uploaded on the Python Package Index (PyPI) and noted that these packages most likely were created using automation tools. “The malicious code is located within each package’s setup.py file, enabling automatic execution upon installation,” Checkmarx explained. “Upon execution, the malicious code within the setup.py file attempted to retrieve an additional payload from a remote server. The URL for the payload was dynamically constructed by appending the package name as a query parameter.” The end result is an info-stealer designed to harvest sensitive information from the victim’s machine and a persistence mechanism to ensure it remained active on the compromised system even after the initial execution. “The discovery of these malicious Python packages on PyPI highlights the ongoing nature of cybersecurity threats within the software development ecosystem. This incident is not an isolated case, and similar attacks targeting package repositories and software supply chains are likely to continue,” the company warned.", "explicit_triplets": [{"subject": "malware", "relation": "uploaded to", "object": "Python Package Index (PyPI) repository"}, {"subject": "Checkmarx", "relation": "warned about", "object": "malicious Python packages"}, {"subject": "malicious Python packages", "relation": "distributed using", "object": "typo-squatting techniques"}, {"subject": "malicious payload", "relation": "aimed to steal", "object": "crypto wallets"}, {"subject": "malicious payload", "relation": "aimed to steal", "object": "sensitive data from browsers"}, {"subject": "malicious payload", "relation": "employed", "object": "persistence mechanism"}, {"subject": "malicious payload", "relation": "attempted to retrieve", "object": "additional payload from a remote server"}, {"subject": "malicious Python packages", "relation": "created using", "object": "automation tools"}, {"subject": "malicious code", "relation": "located within", "object": "setup.py file"}, {"subject": "info-stealer", "relation": "designed to harvest", "object": "sensitive information"}, {"subject": "info-stealer", "relation": "uses", "object": "persistence mechanism"}, {"subject": "malicious Python packages", "relation": "highlight", "object": "ongoing cybersecurity threats within the software development ecosystem"}], "entities": [{"entity_id": 0, "entity_name": "malware", "entity_type": "Malware", "mentions": []}, {"entity_id": 1, "entity_name": "Python Package Index (PyPI) repository", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 2, "entity_name": "Checkmarx", "entity_type": "Organization", "mentions": []}, {"entity_id": 3, "entity_name": "malicious Python packages", "entity_type": "Malware", "mentions": []}, {"entity_id": 4, "entity_name": "typo-squatting techniques", "entity_type": "Malware Characteristic:Behavior", "mentions": []}, {"entity_id": 5, "entity_name": "malicious payload", "entity_type": "Malware Characteristic:Payload", "mentions": []}, {"entity_id": 6, "entity_name": "crypto wallets", "entity_type": "Credential", "mentions": []}, {"entity_id": 8, "entity_name": "persistence mechanism", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 9, "entity_name": "additional payload from a remote server", "entity_type": "Malware Characteristic:Payload", "mentions": []}, {"entity_id": 10, "entity_name": "automation tools", "entity_type": "Tool", "mentions": []}, {"entity_id": 11, "entity_name": "malicious code", "entity_type": "Malware", "mentions": []}, {"entity_id": 12, "entity_name": "setup.py file", "entity_type": "Indicator:File", "mentions": []}, {"entity_id": 13, "entity_name": "info-stealer", "entity_type": "Malware", "mentions": []}, {"entity_id": 7, "entity_name": "sensitive information", "entity_type": "Information", "mentions": ["sensitive data from browsers"]}, {"entity_id": 14, "entity_name": "ongoing cybersecurity threats within the software development ecosystem", "entity_type": "Event", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 1, "entity_text": "Python Package Index (PyPI) repository"}, "relation": "was targeted with", "object": {"entity_id": 5, "entity_text": "malicious payload"}}, {"subject": {"entity_id": 3, "entity_text": "malicious Python packages"}, "relation": "contain", "object": {"entity_id": 5, "entity_text": "malicious payload"}}, {"subject": {"entity_id": 11, "entity_text": "malicious code"}, "relation": "retrieves", "object": {"entity_id": 5, "entity_text": "malicious payload"}}]}