{"text": "The notorious North Korean threat group known as Lazarus exploited a Windows zero-day vulnerability for privilege escalation in attacks involving a rootkit named FudModule, according to cybersecurity firm Avast. The vulnerability is tracked as CVE-2024-21338 and it was observed by <PERSON><PERSON> in Lazarus attacks last year. The security company developed a proof-of-concept (PoC) exploit and sent it to Microsoft in August 2023. The flaw was patched by Microsoft with the company’s February 2024 Patch Tuesday updates, but the initial advisory for CVE-2024-21338 did not list it as a zero-day. The vulnerability impacts the ‘appid.sys’ driver associated with Microsoft’s AppLocker security feature. By targeting a vulnerability in a driver that is present on many systems — rather than using a bring your own vulnerable driver (BYOVD) approach — the attacker benefits from a higher degree of stealth. “By exploiting such a vulnerability, the attacker is in a sense living off the land with no need to bring, drop, or load any custom drivers, making it possible for a kernel attack to be truly fileless. This not only evades most detection mechanisms but also enables the attack on systems where driver allowlisting is in place,” <PERSON><PERSON> explained. By exploiting CVE-2024-21338, Lazarus hackers were able to elevate their privileges on the compromised system and establish a kernel read/write primitive. This enabled them to perform direct kernel object manipulation in an updated version of the FudModule rootkit, which came to light in 2022. ", "explicit_triplets": [{"subject": "<PERSON>", "relation": "exploited", "object": "Windows zero-day vulnerability"}, {"subject": "Windows zero-day vulnerability", "relation": "is tracked as", "object": "CVE-2024-21338"}, {"subject": "<PERSON><PERSON>", "relation": "observed", "object": "CVE-2024-21338"}, {"subject": "<PERSON><PERSON>", "relation": "sent PoC exploit to", "object": "Microsoft"}, {"subject": "Microsoft", "relation": "patched", "object": "CVE-2024-21338"}, {"subject": "CVE-2024-21338", "relation": "impacts", "object": "'appid.sys' driver"}, {"subject": "<PERSON>", "relation": "used", "object": "FudModule rootkit"}, {"subject": "FudModule rootkit", "relation": "came to light in", "object": "2022"}, {"subject": "<PERSON>", "relation": "targeted", "object": "'appid.sys' driver"}, {"subject": "CVE-2024-21338", "relation": "enables", "object": "privilege escalation"}, {"subject": "<PERSON>", "relation": "established", "object": "kernel read/write primitive"}], "entities": [{"entity_id": 0, "entity_name": "<PERSON>", "entity_type": "Attacker", "mentions": []}, {"entity_id": 1, "entity_name": "Windows zero-day vulnerability", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 2, "entity_name": "CVE-2024-21338", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 3, "entity_name": "<PERSON><PERSON>", "entity_type": "Organization", "mentions": []}, {"entity_id": 4, "entity_name": "Microsoft", "entity_type": "Organization", "mentions": []}, {"entity_id": 5, "entity_name": "'appid.sys' driver", "entity_type": "Indicator:File", "mentions": []}, {"entity_id": 6, "entity_name": "FudModule rootkit", "entity_type": "Malware", "mentions": []}, {"entity_id": 7, "entity_name": "2022", "entity_type": "Time", "mentions": []}, {"entity_id": 8, "entity_name": "privilege escalation", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 9, "entity_name": "kernel read/write primitive", "entity_type": "Malware Characteristic:Capability", "mentions": []}], "implicit_triplets": []}