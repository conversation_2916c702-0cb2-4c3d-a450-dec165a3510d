{"text": "The Android banking malware known as Vultur has been updated with new capabilities, allowing operators to interact with the infected devices and modify files, according to a report from security consulting outfit NCC Group. Vultur was first documented in March 2021, when it stood out for the abuse of the legitimate applications AlphaVNC and ngrok for remotely accessing the VNC server on the victim device, and for automating screen recording and key-logging for credential harvesting. The most recent version of the banking malware, however, packs significantly more capabilities, allowing attackers to control the infected device, prevent applications from running, display custom notifications, bypass lock-screen protections, and download, upload, install, search for, and delete files. The infection chain starts with a SMS message instructing the victim to call a phone number to resolve a large transaction that they did not authorize. During the call, a second SMS message that includes a link to a modified McAfee Security package is received. The modified application contains the functionality of the legitimate McAfee Security software, along with the dropper-framework called Brunhilda, which deploys Vultur via three payloads, the last two designed to invoke each other’s functionality. “The message sent by the malware operator through FCM can contain a command, which, upon receipt, triggers the execution of corresponding functionality within the malware. This eliminates the need for an ongoing connection with the device,” NCC Group explains. The latest version of Vultur can also prevent the user from interacting with applications on the device, which are defined in a list provided by the attacker.", "explicit_triplets": [{"subject": "<PERSON><PERSON><PERSON>", "relation": "is", "object": "Android banking malware"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "was first documented in", "object": "March 2021"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "abuses", "object": "the legitimate applications AlphaVNC and ngrok"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "allows attackers to", "object": "control the infected device"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "allows attackers to", "object": "prevent applications from running"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "allows attackers to", "object": "display custom notifications"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "allows attackers to", "object": "bypass lock-screen protections"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "allows attackers to", "object": "download, upload, install, search for, and delete files"}, {"subject": "infection chain", "relation": "starts with", "object": "a SMS message instructing the victim to call a phone number"}, {"subject": "victim", "relation": "receives", "object": "second SMS message that includes a link to a modified McAfee Security package"}, {"subject": "modified McAfee Security package", "relation": "contains", "object": "dropper-framework Brunhilda"}, {"subject": "<PERSON><PERSON><PERSON><PERSON>", "relation": "deploys", "object": "Vultur via three payloads"}, {"subject": "<PERSON><PERSON><PERSON>", "relation": "prevents user from interacting with", "object": "applications defined in a list provided by the attacker"}], "entities": [{"entity_id": 0, "entity_name": "<PERSON><PERSON><PERSON>", "entity_type": "Malware", "mentions": []}, {"entity_id": 1, "entity_name": "Android banking malware", "entity_type": "Malware", "mentions": []}, {"entity_id": 2, "entity_name": "March 2021", "entity_type": "Time", "mentions": []}, {"entity_id": 3, "entity_name": "the legitimate applications AlphaVNC and ngrok", "entity_type": "Tool", "mentions": []}, {"entity_id": 4, "entity_name": "control the infected device", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 5, "entity_name": "prevent applications from running", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 6, "entity_name": "display custom notifications", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 7, "entity_name": "bypass lock-screen protections", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 8, "entity_name": "download, upload, install, search for, and delete files", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 9, "entity_name": "infection chain", "entity_type": "Malware Characteristic:Behavior", "mentions": []}, {"entity_id": 10, "entity_name": "a SMS message instructing the victim to call a phone number", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 11, "entity_name": "victim", "entity_type": "Account", "mentions": []}, {"entity_id": 12, "entity_name": "second SMS message that includes a link to a modified McAfee Security package", "entity_type": "Indicator:URL", "mentions": []}, {"entity_id": 13, "entity_name": "modified McAfee Security package", "entity_type": "Malware", "mentions": []}, {"entity_id": 14, "entity_name": "dropper-framework Brunhilda", "entity_type": "Malware", "mentions": []}, {"entity_id": 15, "entity_name": "<PERSON><PERSON><PERSON><PERSON>", "entity_type": "Malware", "mentions": []}, {"entity_id": 16, "entity_name": "Vultur via three payloads", "entity_type": "Malware Characteristic:Payload", "mentions": []}, {"entity_id": 17, "entity_name": "applications defined in a list provided by the attacker", "entity_type": "Malware Characteristic:Behavior", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 9, "entity_text": "infection chain"}, "relation": "deploys", "object": {"entity_id": 0, "entity_text": "<PERSON><PERSON><PERSON>"}}, {"subject": {"entity_id": 12, "entity_text": "second SMS message that includes a link to a modified McAfee Security package"}, "relation": "is used to distribute", "object": {"entity_id": 0, "entity_text": "<PERSON><PERSON><PERSON>"}}, {"subject": {"entity_id": 13, "entity_text": "modified McAfee Security package"}, "relation": "is used to deploy", "object": {"entity_id": 0, "entity_text": "<PERSON><PERSON><PERSON>"}}, {"subject": {"entity_id": 15, "entity_text": "<PERSON><PERSON><PERSON><PERSON>"}, "relation": "deploys", "object": {"entity_id": 0, "entity_text": "<PERSON><PERSON><PERSON>"}}]}