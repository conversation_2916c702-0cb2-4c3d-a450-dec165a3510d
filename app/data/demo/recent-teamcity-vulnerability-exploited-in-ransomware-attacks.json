{"text": "A TeamCity vulnerability disclosed recently in controversial circumstances is being exploited in ransomware attacks, according to the product’s developer and cybersecurity companies. On March 4, JetBrains, the developer of the TeamCity build management and continuous integration server, announced fixes for CVE-2024-27198 and CVE-2024-27199, two serious authentication bypass vulnerabilities. CVE-2024-27198, which has been rated critical, can be exploited by remote, unauthenticated attackers to take complete control of a server by creating a new admin user account or by generating an admin access token. Rapid7, whose researchers discovered the vulnerabilities, made public details of CVE-2024-27198 and CVE-2024-27199 a few hours after JetBrains announced fixes. Full disclosure seems to have occurred due to miscommunication between the two companies. Rapid7 was concerned that JetBrains would try to silently patch the vulnerabilities and the vendor was concerned that Rapid7 would disclose details too quickly. JetBrains informed customers about patches without notifying Rapid7, which decided to immediately This led to threat actors beginning to target CVE-2024-27198 shortly after disclosure on March 4. By March 6, LeakIX, a project that scans the web for vulnerable and misconfigured systems, started seeing mass exploitation, with signs of rogue user creation seen in 1,400 instances. GuidePoint Security reported on Friday that a ransomware group named <PERSON><PERSON><PERSON><PERSON>, which has been known to target critical infrastructure, may have exploited CVE-2024-27198 for initial access. ", "explicit_triplets": [{"subject": "TeamCity", "relation": "has vulnerability", "object": "CVE-2024-27198"}, {"subject": "TeamCity", "relation": "has vulnerability", "object": "CVE-2024-27199"}, {"subject": "JetBrains", "relation": "announced fixes for", "object": "CVE-2024-27198"}, {"subject": "JetBrains", "relation": "announced fixes for", "object": "CVE-2024-27199"}, {"subject": "CVE-2024-27198", "relation": "could be exploited by", "object": "remote, unauthenticated attackers"}, {"subject": "remote, unauthenticated attackers", "relation": "can create", "object": "new admin user account"}, {"subject": "remote, unauthenticated attackers", "relation": "can generate", "object": "admin access token"}, {"subject": "Rapid7", "relation": "discovered", "object": "CVE-2024-27198"}, {"subject": "Rapid7", "relation": "discovered", "object": "CVE-2024-27199"}, {"subject": "Rapid7", "relation": "made public details of", "object": "CVE-2024-27198"}, {"subject": "Rapid7", "relation": "made public details of", "object": "CVE-2024-27199"}, {"subject": "threat actors", "relation": "began targeting", "object": "CVE-2024-27198"}, {"subject": "LeakIX", "relation": "started seeing", "object": "mass exploitation"}, {"subject": "mass exploitation", "relation": "occurred on", "object": "March 6"}, {"subject": "mass exploitation", "relation": "resulted in", "object": "rogue user creation in 1,400 instances"}, {"subject": "<PERSON>ian<PERSON><PERSON>", "relation": "may have exploited", "object": "CVE-2024-27198"}, {"subject": "<PERSON>ian<PERSON><PERSON>", "relation": "targets", "object": "critical infrastructure"}, {"subject": "CVE-2024-27198", "relation": "used by attackers for", "object": "initial access"}], "entities": [{"entity_id": 0, "entity_name": "TeamCity", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 1, "entity_name": "CVE-2024-27198", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 2, "entity_name": "CVE-2024-27199", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 3, "entity_name": "JetBrains", "entity_type": "Organization", "mentions": []}, {"entity_id": 4, "entity_name": "remote, unauthenticated attackers", "entity_type": "Attacker", "mentions": []}, {"entity_id": 5, "entity_name": "new admin user account", "entity_type": "Account", "mentions": []}, {"entity_id": 6, "entity_name": "admin access token", "entity_type": "Credential", "mentions": []}, {"entity_id": 7, "entity_name": "Rapid7", "entity_type": "Organization", "mentions": []}, {"entity_id": 8, "entity_name": "threat actors", "entity_type": "Attacker", "mentions": []}, {"entity_id": 9, "entity_name": "LeakIX", "entity_type": "Organization", "mentions": []}, {"entity_id": 10, "entity_name": "mass exploitation", "entity_type": "Event", "mentions": []}, {"entity_id": 11, "entity_name": "March 6", "entity_type": "Time", "mentions": []}, {"entity_id": 12, "entity_name": "rogue user creation in 1,400 instances", "entity_type": "Malware Characteristic:Behavior", "mentions": []}, {"entity_id": 13, "entity_name": "<PERSON>ian<PERSON><PERSON>", "entity_type": "Attacker", "mentions": []}, {"entity_id": 14, "entity_name": "critical infrastructure", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 15, "entity_name": "initial access", "entity_type": "Malware Characteristic:Behavior", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 1, "entity_text": "CVE-2024-27198"}, "relation": "is subjected to", "object": {"entity_id": 10, "entity_text": "mass exploitation"}}]}