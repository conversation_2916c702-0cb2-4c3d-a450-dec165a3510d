{"text": "CISA and the FBI warned today that threat actors using Androxgh0st malware are building a botnet focused on cloud credential theft and using the stolen information to deliver additional malicious payloads. It scans for websites and servers vulnerable to the following remote code execution (RCE) vulnerabilities: CVE-2017-9841 (PHPUnit unit testing framework), CVE-2021-41773 (Apache HTTP Server), and CVE-2018-15133 (Laravel PHP web framework). Androxgh0st is a Python-scripted malware primarily used to target .env files that contain confidential information, such as credentials for various high profile applications, i.e., Amazon Web Services [AWS], Microsoft Office 365, SendGrid, and Twilio from the Laravel web application framework. Androxgh0st malware also supports numerous functions capable of abusing the Simple Mail Transfer Protocol (SMTP), such as scanning and exploiting exposed credentials and application programming interfaces (APIs), and web shell deployment. Stolen Twilio and SendGrid credentials can be used by the threat actors to conduct spam campaigns impersonating the breached companies.", "explicit_triplets": [{"subject": "Threat actors", "relation": "are using", "object": "Androxgh0st malware"}, {"subject": "Androxgh0st malware", "relation": "is building", "object": "a botnet"}, {"subject": "a botnet", "relation": "is focused on", "object": "cloud credential theft"}, {"subject": "Androxgh0st malware", "relation": "scans for", "object": "websites and servers vulnerable to remote code execution (RCE) vulnerabilities"}, {"subject": "remote code execution (RCE) vulnerabilities", "relation": "include", "object": "CVE-2017-9841"}, {"subject": "remote code execution (RCE) vulnerabilities", "relation": "include", "object": "CVE-2021-41773"}, {"subject": "remote code execution (RCE) vulnerabilities", "relation": "include", "object": "CVE-2018-15133"}, {"subject": "Androxgh0st malware", "relation": "targets", "object": ".env files"}, {"subject": ".env files", "relation": "contain", "object": "credentials for Amazon Web Services [AWS]"}, {"subject": ".env files", "relation": "contain", "object": "credentials for Microsoft Office 365"}, {"subject": ".env files", "relation": "contain", "object": "credentials for SendGrid"}, {"subject": ".env files", "relation": "contain", "object": "credentials for <PERSON><PERSON><PERSON>"}, {"subject": "Androxgh0st malware", "relation": "supports", "object": "functions capable of abusing the Simple Mail Transfer Protocol (SMTP)"}, {"subject": "threat actors", "relation": "use stolen <PERSON><PERSON><PERSON> credentials for", "object": "spam campaigns impersonating breached companies"}, {"subject": "threat actors", "relation": "use stolen SendGrid credentials for", "object": "spam campaigns impersonating breached companies"}], "entities": [{"entity_id": 1, "entity_name": "Androxgh0st malware", "entity_type": "Malware", "mentions": []}, {"entity_id": 2, "entity_name": "a botnet", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 3, "entity_name": "cloud credential theft", "entity_type": "Malware Characteristic:Behavior", "mentions": []}, {"entity_id": 4, "entity_name": "websites and servers vulnerable to remote code execution (RCE) vulnerabilities", "entity_type": "Exploit Target", "mentions": []}, {"entity_id": 5, "entity_name": "remote code execution (RCE) vulnerabilities", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 6, "entity_name": "CVE-2017-9841", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 7, "entity_name": "CVE-2021-41773", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 8, "entity_name": "CVE-2018-15133", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 9, "entity_name": ".env files", "entity_type": "Indicator:File", "mentions": []}, {"entity_id": 10, "entity_name": "credentials for Amazon Web Services [AWS]", "entity_type": "Credential", "mentions": []}, {"entity_id": 11, "entity_name": "credentials for Microsoft Office 365", "entity_type": "Credential", "mentions": []}, {"entity_id": 12, "entity_name": "credentials for SendGrid", "entity_type": "Credential", "mentions": []}, {"entity_id": 13, "entity_name": "credentials for <PERSON><PERSON><PERSON>", "entity_type": "Credential", "mentions": []}, {"entity_id": 14, "entity_name": "functions capable of abusing the Simple Mail Transfer Protocol (SMTP)", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 0, "entity_name": "threat actors", "entity_type": "Attacker", "mentions": ["Threat actors"]}, {"entity_id": 15, "entity_name": "spam campaigns impersonating breached companies", "entity_type": "Malware Characteristic:Behavior", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 1, "entity_text": "Androxgh0st malware"}, "relation": "scans for and exploits", "object": {"entity_id": 5, "entity_text": "remote code execution (RCE) vulnerabilities"}}]}