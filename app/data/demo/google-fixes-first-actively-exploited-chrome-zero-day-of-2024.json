{"text": "Google has released security updates to fix the first Chrome zero-day vulnerability exploited in the wild since the start of the year. ​The high-severity zero-day vulnerability (CVE-2024-0519) is due to a high-severity out-of-bounds memory access weakness in the Chrome V8 JavaScript engine, which remote attackers can exploit via a crafted HTML page to gain access to data beyond the memory buffer through heap corruption, providing them access to sensitive information or triggering a crash. Besides unauthorized access to out-of-bounds memory, CVE-2024-0519 could also be exploited to bypass protection mechanisms such as ASLR to make it easier to achieve code execution via another weakness.", "explicit_triplets": [{"subject": "Google", "relation": "has released security updates to fix", "object": "the first Chrome zero-day vulnerability exploited in the wild since the start of the year"}, {"subject": "Chrome zero-day vulnerability (CVE-2024-0519)", "relation": "is due to", "object": "out-of-bounds memory access weakness in the Chrome V8 JavaScript engine"}, {"subject": "remote attackers", "relation": "can exploit", "object": "Chrome zero-day vulnerability (CVE-2024-0519)"}, {"subject": "Chrome zero-day vulnerability (CVE-2024-0519)", "relation": "is exploited via", "object": "crafted HTML page"}, {"subject": "remote attackers", "relation": "may gain", "object": "access to sensitive information"}, {"subject": "remote attackers", "relation": "may trigger", "object": "a crash"}, {"subject": "Chrome zero-day vulnerability (CVE-2024-0519)", "relation": "could be exploited to bypass", "object": "protection mechanisms such as ASLR"}, {"subject": "protection mechanisms such as ASLR", "relation": "make it easier to achieve", "object": "code execution via another weakness"}], "entities": [{"entity_id": 0, "entity_name": "Google", "entity_type": "Organization", "mentions": []}, {"entity_id": 1, "entity_name": "Chrome zero-day vulnerability (CVE-2024-0519)", "entity_type": "Vulnerability", "mentions": ["the first Chrome zero-day vulnerability exploited in the wild since the start of the year"]}, {"entity_id": 2, "entity_name": "out-of-bounds memory access weakness in the Chrome V8 JavaScript engine", "entity_type": "Malware Characteristic:Behavior", "mentions": []}, {"entity_id": 3, "entity_name": "remote attackers", "entity_type": "Attacker", "mentions": []}, {"entity_id": 4, "entity_name": "crafted HTML page", "entity_type": "Indicator:File", "mentions": []}, {"entity_id": 5, "entity_name": "access to sensitive information", "entity_type": "Information", "mentions": []}, {"entity_id": 6, "entity_name": "a crash", "entity_type": "Event", "mentions": []}, {"entity_id": 7, "entity_name": "protection mechanisms such as ASLR", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 8, "entity_name": "code execution via another weakness", "entity_type": "Malware Characteristic:Capability", "mentions": []}], "implicit_triplets": []}