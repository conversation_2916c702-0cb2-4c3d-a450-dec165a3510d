{"text": "GitHub rotated keys potentially exposed by a vulnerability patched in December that could let attackers access credentials within production containers via environment variables. This unsafe reflection vulnerability (tracked as CVE-2024-0200) can allow attackers to gain remote code execution on unpatched servers. GitHub also fixed a second high-severity Enterprise Server command injection vulnerability (CVE-2024-0507) that would allow attackers using a Management Console user account with an editor role to escalate privileges.", "explicit_triplets": [{"subject": "GitHub", "relation": "rotated keys potentially exposed by", "object": "CVE-2024-0200"}, {"subject": "CVE-2024-0200", "relation": "patched in", "object": "December"}, {"subject": "attackers", "relation": "could access", "object": "credentials within production containers"}, {"subject": "CVE-2024-0200", "relation": "allows", "object": "attackers to gain remote code execution on unpatched servers"}, {"subject": "GitHub", "relation": "fixed", "object": "CVE-2024-0507"}, {"subject": "CVE-2024-0507", "relation": "allows", "object": "attackers using a Management Console user account with an editor role to escalate privileges"}], "entities": [{"entity_id": 0, "entity_name": "GitHub", "entity_type": "Organization", "mentions": []}, {"entity_id": 1, "entity_name": "CVE-2024-0200", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 2, "entity_name": "December", "entity_type": "Time", "mentions": []}, {"entity_id": 3, "entity_name": "attackers", "entity_type": "Attacker", "mentions": []}, {"entity_id": 4, "entity_name": "credentials within production containers", "entity_type": "Credential", "mentions": []}, {"entity_id": 5, "entity_name": "attackers to gain remote code execution on unpatched servers", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 6, "entity_name": "CVE-2024-0507", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 7, "entity_name": "attackers using a Management Console user account with an editor role to escalate privileges", "entity_type": "Malware Characteristic:Capability", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 1, "entity_text": "CVE-2024-0200"}, "relation": "can expose", "object": {"entity_id": 4, "entity_text": "credentials within production containers"}}]}