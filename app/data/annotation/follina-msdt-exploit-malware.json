{"text": "<PERSON><PERSON><PERSON> (CVE-2022-30190) is a vulnerability in the Microsoft Support Diagnostic Tool (MSDT) that allows remote code execution on vulnerable systems through the ms-msdt protocol handler scheme. The bug is present in all supported versions of Windows. The vulnerability can be easily exploited by a specially crafted Word document that downloads and loads a malicious HTML file through Word's remote template feature. The HTML file ultimately allows the attacker to load and execute PowerShell code within Windows. The vulnerability can also be exploited through the RTF file format.", "explicit_triplets": [{"subject": "<PERSON><PERSON><PERSON> (CVE-2022-30190)", "relation": "is a vulnerability in", "object": "Microsoft Support Diagnostic Tool (MSDT)"}, {"subject": "<PERSON><PERSON><PERSON> (CVE-2022-30190)", "relation": "allows", "object": "remote code execution"}, {"subject": "<PERSON><PERSON><PERSON> (CVE-2022-30190)", "relation": "is present in", "object": "Windows"}, {"subject": "<PERSON><PERSON><PERSON> (CVE-2022-30190)", "relation": "is exploited by", "object": "specially crafted Word document"}, {"subject": "specially crafted Word document", "relation": "downloads and loads", "object": "malicious HTML file"}, {"subject": "malicious HTML file", "relation": "allows attacker to execute", "object": "PowerShell code"}, {"subject": "<PERSON><PERSON><PERSON> (CVE-2022-30190)", "relation": "can be exploited through", "object": "RTF file format"}], "entities": [{"entity_id": 0, "entity_name": "<PERSON><PERSON><PERSON> (CVE-2022-30190)", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 1, "entity_name": "Microsoft Support Diagnostic Tool (MSDT)", "entity_type": "Tool", "mentions": []}, {"entity_id": 2, "entity_name": "remote code execution", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 3, "entity_name": "Windows", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 4, "entity_name": "specially crafted Word document", "entity_type": "Indicator:File", "mentions": []}, {"entity_id": 5, "entity_name": "malicious HTML file", "entity_type": "Indicator:File", "mentions": []}, {"entity_id": 6, "entity_name": "PowerShell code", "entity_type": "Malware", "mentions": []}, {"entity_id": 7, "entity_name": "RTF file format", "entity_type": "Indicator:File", "mentions": []}], "implicit_triplets": []}