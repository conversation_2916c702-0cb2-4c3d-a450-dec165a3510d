{"text": "PurpleFox (or 'DirtyMoe') is a modular Windows botnet malware first spotted in 2018 that comes with a rootkit module allowing it to hide and persist between device reboots. It can be used as a downloader that introduces more potent second-stage payloads on compromised systems, offers its operators backdoor capabilities, and can also act as a distributed denial of service (DDoS) bot. In October 2021, researchers noticed that new versions of PurpleFox switched to using WebSocket for command and control (C2) communications for stealth. In January 2022, a campaign spread the malware under the guise of a Telegram desktop app. CERT-UA used IoCs shared by Avast and TrendMicro to identify PurpleFox malware infections on Ukrainian computers, tracking the activity under the identifier 'UAC-0027'. CERT-UA says PurpleFox typically infects systems when victims launch laced MSI installers and highlights its self-propagation capabilities using exploits for known flaws and password brute-forcing.", "explicit_triplets": [{"subject": "PurpleFox", "relation": "is also known as", "object": "DirtyMoe"}, {"subject": "PurpleFox", "relation": "is a", "object": "Windows botnet malware"}, {"subject": "PurpleFox", "relation": "first spotted in", "object": "2018"}, {"subject": "PurpleFox", "relation": "has", "object": "rootkit module"}, {"subject": "PurpleFox", "relation": "can be used as", "object": "downloader"}, {"subject": "PurpleFox", "relation": "offers", "object": "backdoor capabilities"}, {"subject": "PurpleFox", "relation": "can act as", "object": "distributed denial of service (DDoS) bot"}, {"subject": "PurpleFox", "relation": "switched to using", "object": "WebSocket for command and control (C2) communications"}, {"subject": "PurpleFox", "relation": "spread under guise of", "object": "Telegram desktop app"}, {"subject": "CERT-UA", "relation": "used", "object": "IoCs shared by Avast and TrendMicro"}, {"subject": "CERT-UA", "relation": "identified", "object": "PurpleFox malware infections"}, {"subject": "PurpleFox", "relation": "infects systems when victims launch", "object": "laced MSI installers"}, {"subject": "PurpleFox", "relation": "has", "object": "self-propagation capabilities"}, {"subject": "PurpleFox", "relation": "uses", "object": "exploits for known flaws"}, {"subject": "PurpleFox", "relation": "uses", "object": "password brute-forcing"}, {"subject": "PurpleFox", "relation": "tracked under identifier", "object": "UAC-0027"}, {"subject": "PurpleFox", "relation": "infected systems in", "object": "Ukraine"}], "entities": [{"entity_id": 0, "entity_name": "PurpleFox", "entity_type": "Malware", "mentions": ["PurpleFox malware infections"]}, {"entity_id": 1, "entity_name": "DirtyMoe", "entity_type": "Malware", "mentions": []}, {"entity_id": 2, "entity_name": "Windows botnet malware", "entity_type": "Malware", "mentions": []}, {"entity_id": 3, "entity_name": "2018", "entity_type": "Time", "mentions": []}, {"entity_id": 4, "entity_name": "rootkit module", "entity_type": "Malware Characteristic:Feature", "mentions": []}, {"entity_id": 5, "entity_name": "downloader", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 6, "entity_name": "backdoor capabilities", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 7, "entity_name": "distributed denial of service (DDoS) bot", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 8, "entity_name": "WebSocket for command and control (C2) communications", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 9, "entity_name": "Telegram desktop app", "entity_type": "Tool", "mentions": []}, {"entity_id": 10, "entity_name": "CERT-UA", "entity_type": "Organization", "mentions": []}, {"entity_id": 11, "entity_name": "IoCs shared by Avast and TrendMicro", "entity_type": "Indicator", "mentions": []}, {"entity_id": 12, "entity_name": "laced MSI installers", "entity_type": "Indicator:File", "mentions": []}, {"entity_id": 13, "entity_name": "self-propagation capabilities", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 14, "entity_name": "exploits for known flaws", "entity_type": "Exploit Target", "mentions": []}, {"entity_id": 15, "entity_name": "password brute-forcing", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 16, "entity_name": "UAC-0027", "entity_type": "Information", "mentions": []}, {"entity_id": 17, "entity_name": "Ukraine", "entity_type": "Location", "mentions": []}], "implicit_triplets": []}