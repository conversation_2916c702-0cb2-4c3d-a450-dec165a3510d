{"text": "Ransomware groups are abusing unpatched versions of a Linux-based Mitel VoIP (Voice over Internet Protocol) application and using it as a springboard plant malware on targeted systems. The critical remote code execution (RCE) flaw, tracked as CVE-2022-29499, was first report by Crowdstrike in April as a zero-day vulnerability and is now patched. Mitel is popularly known for providing business phone systems and unified communication as a service (UCaaS) to all forms of organizations. The Mitel focuses on VoIP technology allowing users to make phone calls using an internet connection instead of regular telephone lines.", "explicit_triplets": [{"subject": "Ransomware groups", "relation": "are abusing", "object": "Linux-based Mitel VoIP application"}, {"subject": "Linux-based Mitel VoIP application", "relation": "contains", "object": "CVE-2022-29499"}, {"subject": "CVE-2022-29499", "relation": "was first reported in", "object": "April"}, {"subject": "Crowdstrike", "relation": "first reported", "object": "CVE-2022-29499"}, {"subject": "CVE-2022-29499", "relation": "is classified as", "object": "zero-day vulnerability"}, {"subject": "CVE-2022-29499", "relation": "enables", "object": "remote code execution"}, {"subject": "CVE-2022-29499", "relation": "is now", "object": "patched"}, {"subject": "Mitel", "relation": "provides", "object": "business phone systems"}, {"subject": "Mitel", "relation": "provides", "object": "unified communication as a service (UCaaS)"}, {"subject": "Mitel", "relation": "focuses on", "object": "VoIP technology"}], "entities": [{"entity_id": 0, "entity_name": "Ransomware groups", "entity_type": "Attacker", "mentions": []}, {"entity_id": 1, "entity_name": "Linux-based Mitel VoIP application", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 2, "entity_name": "CVE-2022-29499", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 3, "entity_name": "April", "entity_type": "Time", "mentions": []}, {"entity_id": 4, "entity_name": "Crowdstrike", "entity_type": "Organization", "mentions": []}, {"entity_id": 5, "entity_name": "zero-day vulnerability", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 6, "entity_name": "remote code execution", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 7, "entity_name": "patched", "entity_type": "Malware Characteristic:Feature", "mentions": []}, {"entity_id": 8, "entity_name": "Mitel", "entity_type": "Organization", "mentions": []}, {"entity_id": 9, "entity_name": "business phone systems", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 10, "entity_name": "unified communication as a service (UCaaS)", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 11, "entity_name": "VoIP technology", "entity_type": "Infrastructure", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 2, "entity_text": "CVE-2022-29499"}, "relation": "is vulnerability in", "object": {"entity_id": 8, "entity_text": "Mitel"}}]}