{"text": "A new vulnerability, which we refer to as “Migraine” for its involvement with macOS migration, could allow an attacker with root access to automatically bypass System Integrity Protection (SIP) in macOS and perform arbitrary operations on a device. We shared these findings with Apple through Coordinated Vulnerability Disclosure (CVD) via Microsoft Security Vulnerability Research (MSVR). A fix for this vulnerability, now identified as CVE-2023-32369, was included in the security updates released by Apple on May 18, 2023.", "explicit_triplets": [{"subject": "Migraine", "relation": "affects", "object": "macOS"}, {"subject": "attacker", "relation": "could bypass", "object": "System Integrity Protection (SIP)"}, {"subject": "attacker", "relation": "can perform", "object": "arbitrary operations on a device"}, {"subject": "Microsoft Security Vulnerability Research (MSVR)", "relation": "shared findings with", "object": "Apple"}, {"subject": "CVE-2023-32369", "relation": "is identified as", "object": "Migraine"}, {"subject": "Apple", "relation": "released fix for", "object": "CVE-2023-32369"}, {"subject": "Apple", "relation": "released security updates on", "object": "May 18, 2023"}], "entities": [{"entity_id": 0, "entity_name": "Migraine", "entity_type": "Malware", "mentions": []}, {"entity_id": 1, "entity_name": "macOS", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 2, "entity_name": "attacker", "entity_type": "Attacker", "mentions": []}, {"entity_id": 3, "entity_name": "System Integrity Protection (SIP)", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 4, "entity_name": "arbitrary operations on a device", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 5, "entity_name": "Microsoft Security Vulnerability Research (MSVR)", "entity_type": "Organization", "mentions": []}, {"entity_id": 6, "entity_name": "Apple", "entity_type": "Organization", "mentions": []}, {"entity_id": 7, "entity_name": "CVE-2023-32369", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 8, "entity_name": "May 18, 2023", "entity_type": "Time", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 2, "entity_text": "attacker"}, "relation": "exploits vulnerability addressed by", "object": {"entity_id": 6, "entity_text": "Apple"}}]}