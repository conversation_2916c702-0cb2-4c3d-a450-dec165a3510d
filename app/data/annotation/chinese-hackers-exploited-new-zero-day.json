{"text": "arracuda has revealed that Chinese threat actors exploited a new zero-day in its Email Security Gateway (ESG) appliances to deploy backdoors on a limited number of devices. Tracked as CVE-2023-7102, the issue relates to a case of arbitrary code execution that resides within a third-party and open-source library named Spreadsheet::ParseExcel that's used by the Amavis scanner within the gateway to screen Microsoft Excel email attachments for malware. The company attributed the activity to a threat actor tracked by Google-owned Mandiant as UNC4841, which was previously linked to the active exploitation of another zero-day in Barracuda devices (CVE-2023-2868, CVSS score: 9.8) earlier this year.Successful exploitation of the new flaw is accomplished by means of a specially crafted Microsoft Excel email attachment. This is followed by the deployment of new variants of known implants called SEASPY and SALTWATER that are equipped to offer persistence and command execution capabilities.", "explicit_triplets": [{"subject": "Chinese threat actors", "relation": "exploited", "object": "a new zero-day in Barracuda Email Security Gateway appliances"}, {"subject": "a new zero-day in Barracuda Email Security Gateway appliances", "relation": "is tracked as", "object": "CVE-2023-7102"}, {"subject": "CVE-2023-7102", "relation": "relates to", "object": "arbitrary code execution"}, {"subject": "arbitrary code execution", "relation": "resides within", "object": "Spreadsheet::ParseExcel"}, {"subject": "Spreadsheet::ParseExcel", "relation": "is used by", "object": "<PERSON><PERSON><PERSON> scanner"}, {"subject": "<PERSON><PERSON><PERSON> scanner", "relation": "screens", "object": "Microsoft Excel email attachments"}, {"subject": "Barr<PERSON><PERSON>", "relation": "attributed the activity to", "object": "UNC4841"}, {"subject": "UNC4841", "relation": "linked to exploitation of", "object": "CVE-2023-2868"}, {"subject": "CVE-2023-2868", "relation": "affects", "object": "Barracuda devices"}, {"subject": "Microsoft Excel email attachment", "relation": "used for exploiting", "object": "CVE-2023-7102"}, {"subject": "CVE-2023-7102", "relation": "results in the deployment of", "object": "SEASPY"}, {"subject": "CVE-2023-7102", "relation": "results in the deployment of", "object": "SALTWATER"}, {"subject": "SEASPY", "relation": "offers", "object": "persistence and command execution capabilities"}, {"subject": "SALTWATER", "relation": "offers", "object": "persistence and command execution capabilities"}], "entities": [{"entity_id": 0, "entity_name": "Chinese threat actors", "entity_type": "Attacker", "mentions": []}, {"entity_id": 1, "entity_name": "a new zero-day in Barracuda Email Security Gateway appliances", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 2, "entity_name": "CVE-2023-7102", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 3, "entity_name": "arbitrary code execution", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 4, "entity_name": "Spreadsheet::ParseExcel", "entity_type": "Tool", "mentions": []}, {"entity_id": 5, "entity_name": "<PERSON><PERSON><PERSON> scanner", "entity_type": "Tool", "mentions": []}, {"entity_id": 7, "entity_name": "Barr<PERSON><PERSON>", "entity_type": "Organization", "mentions": []}, {"entity_id": 8, "entity_name": "UNC4841", "entity_type": "Attacker", "mentions": []}, {"entity_id": 9, "entity_name": "CVE-2023-2868", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 10, "entity_name": "Barracuda devices", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 6, "entity_name": "Microsoft Excel email attachment", "entity_type": "Indicator:File", "mentions": ["Microsoft Excel email attachments"]}, {"entity_id": 11, "entity_name": "SEASPY", "entity_type": "Malware", "mentions": []}, {"entity_id": 12, "entity_name": "SALTWATER", "entity_type": "Malware", "mentions": []}, {"entity_id": 13, "entity_name": "persistence and command execution capabilities", "entity_type": "Malware Characteristic:Capability", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 9, "entity_text": "CVE-2023-2868"}, "relation": "is related to exploitation by the same actor as", "object": {"entity_id": 2, "entity_text": "CVE-2023-7102"}}]}