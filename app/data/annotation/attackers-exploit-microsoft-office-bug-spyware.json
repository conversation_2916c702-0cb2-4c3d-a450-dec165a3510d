{"text": "Attackers are exploiting a 6-year-old Microsoft Office remote code execution (RCE) flaw to deliver spyware, in an email campaign weaponized by malicious Excel attachments and characterized by sophisticated evasion tactics. Threat actors dangle lures relating to business activity in spam emails that deliver files that contain CVE-2017-11882, an RCE flaw that dates back to 2014 and can allow for system takeover, <PERSON><PERSON><PERSON> revealed in a blog post published Dec. 19. The end goal of the attack is to load Agent Tesla, a remote access Trojan (RAT) and advanced keylogger first discovered in 2014, and exfiltrate credentials and other data from an infected system via a Telegram bot run by the attackers.", "explicit_triplets": [{"subject": "Attackers", "relation": "are exploiting", "object": "CVE-2017-11882"}, {"subject": "CVE-2017-11882", "relation": "is a", "object": "remote code execution flaw"}, {"subject": "Malicious Excel attachments", "relation": "contain", "object": "CVE-2017-11882"}, {"subject": "Threat actors", "relation": "use lures relating to", "object": "business activity"}, {"subject": "Email campaign", "relation": "delivers", "object": "Agent <PERSON><PERSON>"}, {"subject": "Agent <PERSON><PERSON>", "relation": "is a type of", "object": "remote access Trojan"}, {"subject": "Agent <PERSON><PERSON>", "relation": "is a type of", "object": "advanced keylogger"}, {"subject": "Agent <PERSON><PERSON>", "relation": "was first discovered in", "object": "2014"}, {"subject": "Agent <PERSON><PERSON>", "relation": "exfiltrates data via", "object": "Telegram bot"}], "entities": [{"entity_id": 0, "entity_name": "Attackers", "entity_type": "Attacker", "mentions": []}, {"entity_id": 1, "entity_name": "CVE-2017-11882", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 2, "entity_name": "remote code execution flaw", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 3, "entity_name": "Malicious Excel attachments", "entity_type": "Indicator:File", "mentions": []}, {"entity_id": 4, "entity_name": "Threat actors", "entity_type": "Attacker", "mentions": []}, {"entity_id": 5, "entity_name": "business activity", "entity_type": "Information", "mentions": []}, {"entity_id": 6, "entity_name": "Email campaign", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 7, "entity_name": "Agent <PERSON><PERSON>", "entity_type": "Malware", "mentions": []}, {"entity_id": 8, "entity_name": "remote access Trojan", "entity_type": "Malware", "mentions": []}, {"entity_id": 9, "entity_name": "advanced keylogger", "entity_type": "Malware", "mentions": []}, {"entity_id": 10, "entity_name": "2014", "entity_type": "Time", "mentions": []}, {"entity_id": 11, "entity_name": "Telegram bot", "entity_type": "Tool", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 1, "entity_text": "CVE-2017-11882"}, "relation": "is used to deliver", "object": {"entity_id": 7, "entity_text": "Agent <PERSON><PERSON>"}}, {"subject": {"entity_id": 4, "entity_text": "Threat actors"}, "relation": "deliver", "object": {"entity_id": 7, "entity_text": "Agent <PERSON><PERSON>"}}]}