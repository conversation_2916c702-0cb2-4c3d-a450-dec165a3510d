{"text": "Attackers are weaponizing an old Microsoft Office vulnerability as part of phishing campaigns to distribute a strain of malware called Agent Tesla. The infection chains leverage decoy Excel documents attached in invoice-themed messages to trick potential targets into opening them and activate the exploitation of CVE-2017-11882 (CVSS score: 7.8), a memory corruption vulnerability in Office's Equation Editor that could result in code execution with the privileges of the user. The findings, which come from Zscaler ThreatLabz, build on prior reports from Fortinet FortiGuard Labs, which detailed a similar phishing campaign that exploited the security flaw to deliver the malware. Once a user downloads a malicious attachment and opens it, if their version of Microsoft Excel is vulnerable, the Excel file initiates communication with a malicious destination and proceeds to download additional files without requiring any further user interaction. The first payload is an obfuscated Visual Basic Script, which initiates the download of a malicious JPG file that comes embedded with a Base64-encoded DLL file.", "explicit_triplets": [{"subject": "Attackers", "relation": "are weaponizing", "object": "Microsoft Office vulnerability"}, {"subject": "Attackers", "relation": "distribute", "object": "Agent <PERSON><PERSON>"}, {"subject": "Phishing campaigns", "relation": "leverage", "object": "Excel documents"}, {"subject": "Excel documents", "relation": "activate exploitation of", "object": "CVE-2017-11882"}, {"subject": "CVE-2017-11882", "relation": "is a vulnerability in", "object": "Office's Equation Editor"}, {"subject": "User", "relation": "downloads and opens", "object": "malicious attachment"}, {"subject": "Excel file", "relation": "initiates communication with", "object": "malicious destination"}, {"subject": "Excel file", "relation": "downloads", "object": "additional files"}, {"subject": "Visual Basic Script", "relation": "initiates download of", "object": "malicious JPG file"}, {"subject": "Malicious JPG file", "relation": "embedded with", "object": "Base64-encoded DLL file"}, {"subject": "Fortinet FortiGuard Labs", "relation": "detailed", "object": "phishing campaign"}], "entities": [{"entity_id": 0, "entity_name": "Attackers", "entity_type": "Attacker", "mentions": []}, {"entity_id": 1, "entity_name": "Microsoft Office vulnerability", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 2, "entity_name": "Agent <PERSON><PERSON>", "entity_type": "Malware", "mentions": []}, {"entity_id": 5, "entity_name": "CVE-2017-11882", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 6, "entity_name": "Office's Equation Editor", "entity_type": "Tool", "mentions": []}, {"entity_id": 7, "entity_name": "User", "entity_type": "Account", "mentions": []}, {"entity_id": 4, "entity_name": "Excel file", "entity_type": "Indicator:File", "mentions": ["Excel documents"]}, {"entity_id": 9, "entity_name": "malicious destination", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 10, "entity_name": "additional files", "entity_type": "Indicator:File", "mentions": []}, {"entity_id": 11, "entity_name": "Visual Basic Script", "entity_type": "Tool", "mentions": []}, {"entity_id": 8, "entity_name": "malicious JPG file", "entity_type": "Indicator:File", "mentions": ["Malicious JPG file", "malicious attachment"]}, {"entity_id": 12, "entity_name": "Base64-encoded DLL file", "entity_type": "Indicator:File", "mentions": []}, {"entity_id": 13, "entity_name": "Fortinet FortiGuard Labs", "entity_type": "Organization", "mentions": []}, {"entity_id": 3, "entity_name": "phishing campaign", "entity_type": "Event", "mentions": ["Phishing campaigns"]}], "implicit_triplets": [{"subject": {"entity_id": 0, "entity_text": "Attackers"}, "relation": "use", "object": {"entity_id": 4, "entity_text": "Excel file"}}, {"subject": {"entity_id": 4, "entity_text": "Excel file"}, "relation": "initiates the download of", "object": {"entity_id": 8, "entity_text": "malicious JPG file"}}]}