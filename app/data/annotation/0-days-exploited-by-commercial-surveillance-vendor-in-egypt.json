{"text": "Last week Google’s Threat Analysis Group (TAG), in partnership with The Citizen Lab, discovered an in-the-wild 0-day exploit chain for iPhones. Developed by the commercial surveillance vendor, Intellexa, this exploit chain is used to install its Predator spyware surreptitiously onto a device. In response, yesterday, Apple patched the bugs in iOS 16.7 and iOS 17.0.1 as CVE-2023-41991, CVE-2023-41992, CVE-2023-41993. This quick patching from Apple helps to better protect users and we encourage all iOS users to install them as soon as possible.", "explicit_triplets": [{"subject": "Google's Threat Analysis Group (TAG)", "relation": "discovered", "object": "an in-the-wild 0-day exploit chain"}, {"subject": "The Citizen Lab", "relation": "discovered", "object": "an in-the-wild 0-day exploit chain"}, {"subject": "an in-the-wild 0-day exploit chain", "relation": "targets", "object": "iPhones"}, {"subject": "Intellexa", "relation": "developed", "object": "an in-the-wild 0-day exploit chain"}, {"subject": "an in-the-wild 0-day exploit chain", "relation": "is used to install", "object": "Predator spyware"}, {"subject": "Apple", "relation": "patched", "object": "CVE-2023-41991"}, {"subject": "Apple", "relation": "patched", "object": "CVE-2023-41992"}, {"subject": "Apple", "relation": "patched", "object": "CVE-2023-41993"}, {"subject": "CVE-2023-41991", "relation": "patched in", "object": "iOS 16.7 and iOS 17.0.1"}, {"subject": "CVE-2023-41992", "relation": "patched in", "object": "iOS 16.7 and iOS 17.0.1"}, {"subject": "CVE-2023-41993", "relation": "patched in", "object": "iOS 16.7 and iOS 17.0.1"}], "entities": [{"entity_id": 0, "entity_name": "Google's Threat Analysis Group (TAG)", "entity_type": "Organization", "mentions": []}, {"entity_id": 1, "entity_name": "an in-the-wild 0-day exploit chain", "entity_type": "Exploit Target", "mentions": []}, {"entity_id": 2, "entity_name": "The Citizen Lab", "entity_type": "Organization", "mentions": []}, {"entity_id": 3, "entity_name": "iPhones", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 4, "entity_name": "Intellexa", "entity_type": "Organization", "mentions": []}, {"entity_id": 5, "entity_name": "Predator spyware", "entity_type": "Malware", "mentions": []}, {"entity_id": 6, "entity_name": "Apple", "entity_type": "Organization", "mentions": []}, {"entity_id": 7, "entity_name": "CVE-2023-41991", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 8, "entity_name": "CVE-2023-41992", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 9, "entity_name": "CVE-2023-41993", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 10, "entity_name": "iOS 16.7 and iOS 17.0.1", "entity_type": "Infrastructure", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 10, "entity_text": "iOS 16.7 and iOS 17.0.1"}, "relation": "patched", "object": {"entity_id": 1, "entity_text": "an in-the-wild 0-day exploit chain"}}]}