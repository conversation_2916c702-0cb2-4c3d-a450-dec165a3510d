{"text": "Since early October 2023, Microsoft has observed two North Korean nation-state threat actors – Diamond <PERSON>t and Onyx <PERSON>leet – exploiting CVE-2023-42793, a remote-code execution vulnerability affecting multiple versions of JetBrains TeamCity server. TeamCity is a continuous integration/continuous deployment (CI/CD) application used by organizations for DevOps and other software development activities. While the two threat actors are exploiting the same vulnerability, Microsoft observed Diamond Sleet and Onyx Sleet utilizing unique sets of tools and techniques following successful exploitation. Based on the profile of victim organizations affected by these intrusions, Microsoft assesses that the threat actors may be opportunistically compromising vulnerable servers. However, both actors have deployed malware and tools and utilized techniques that may enable persistent access to victim environments.", "explicit_triplets": [{"subject": "Microsoft", "relation": "observed", "object": "Diamond Sleet"}, {"subject": "Microsoft", "relation": "observed", "object": "Onyx Sleet"}, {"subject": "Diamond Sleet", "relation": "exploiting", "object": "CVE-2023-42793"}, {"subject": "Onyx Sleet", "relation": "exploiting", "object": "CVE-2023-42793"}, {"subject": "CVE-2023-42793", "relation": "is", "object": "a remote-code execution vulnerability"}, {"subject": "CVE-2023-42793", "relation": "affects", "object": "JetBrains TeamCity server"}, {"subject": "JetBrains TeamCity server", "relation": "is", "object": "Application"}, {"subject": "Diamond Sleet", "relation": "utilizes", "object": "unique sets of tools and techniques"}, {"subject": "Onyx Sleet", "relation": "utilizes", "object": "unique sets of tools and techniques"}, {"subject": "Diamond Sleet", "relation": "deployed", "object": "malware and tools"}, {"subject": "Onyx Sleet", "relation": "deployed", "object": "malware and tools"}, {"subject": "Diamond Sleet", "relation": "utilized", "object": "techniques that may enable persistent access"}, {"subject": "Onyx Sleet", "relation": "utilized", "object": "techniques that may enable persistent access"}], "entities": [{"entity_id": 0, "entity_name": "Microsoft", "entity_type": "Organization", "mentions": []}, {"entity_id": 1, "entity_name": "Diamond Sleet", "entity_type": "Attacker", "mentions": []}, {"entity_id": 2, "entity_name": "Onyx Sleet", "entity_type": "Attacker", "mentions": []}, {"entity_id": 3, "entity_name": "CVE-2023-42793", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 4, "entity_name": "a remote-code execution vulnerability", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 5, "entity_name": "JetBrains TeamCity server", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 6, "entity_name": "Application", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 7, "entity_name": "unique sets of tools and techniques", "entity_type": "Tool", "mentions": []}, {"entity_id": 8, "entity_name": "malware and tools", "entity_type": "Malware", "mentions": []}, {"entity_id": 9, "entity_name": "techniques that may enable persistent access", "entity_type": "Malware Characteristic:Behavior", "mentions": []}], "implicit_triplets": []}