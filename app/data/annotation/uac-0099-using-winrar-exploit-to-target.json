{"text": "The threat actor known as UAC-0099 has been linked to continued attacks aimed at Ukraine, some of which leverage a high-severity flaw in the WinRAR software to deliver a malware strain called LONEPAGE. The threat actor targets Ukrainian employees working for companies outside of Ukraine. UAC-0099 was first documented by the Computer Emergency Response Team of Ukraine (CERT-UA) in June 2023, detailing its attacks against state organizations and media entities for espionage motives. The attack chains leveraged phishing messages containing HTA, RAR, and LNK file attachments that led to the deployment of LONEPAGE, a Visual Basic Script (VBS) malware that's capable of contacting a command-and-control (C2) server to retrieve additional payloads such as keyloggers, stealers, and screenshot malware. The latest analysis from Deep Instinct reveals that the use of HTA attachments is just one of three different infection chains, the other two of which leverage self-extracting (SFX) archives and booby-trapped ZIP files. The ZIP files exploit the WinRAR vulnerability (CVE-2023-38831, CVSS score: 7.8) to distribute LONEPAGE.", "explicit_triplets": [{"subject": "Threat Actor UAC-0099", "relation": "linked to continued attacks aimed at", "object": "Ukraine"}, {"subject": "Threat Actor UAC-0099", "relation": "leverages", "object": "WinRAR vulnerability CVE-2023-38831"}, {"subject": "WinRAR vulnerability CVE-2023-38831", "relation": "is exploited to deliver", "object": "LONEPAGE"}, {"subject": "Threat Actor UAC-0099", "relation": "targets", "object": "Ukrainian employees working for companies outside of Ukraine"}, {"subject": "Threat Actor UAC-0099", "relation": "first documented by", "object": "Computer Emergency Response Team of Ukraine (CERT-UA)"}, {"subject": "Computer Emergency Response Team of Ukraine (CERT-UA)", "relation": "documented UAC-0099 in", "object": "June 2023"}, {"subject": "Threat Actor UAC-0099", "relation": "attacked", "object": "state organizations and media entities"}, {"subject": "LONEPAGE", "relation": "has capability", "object": "contacting a command-and-control (C2) server"}, {"subject": "LONEPAGE", "relation": "retrieves additional payloads such as", "object": "keyloggers"}, {"subject": "LONEPAGE", "relation": "retrieves additional payloads such as", "object": "stealers"}, {"subject": "LONEPAGE", "relation": "retrieves additional payloads such as", "object": "screenshot malware"}, {"subject": "UAC-0099 attacks", "relation": "uses infection chains via", "object": "HTA attachments"}, {"subject": "UAC-0099 attacks", "relation": "uses infection chains via", "object": "self-extracting (SFX) archives"}, {"subject": "UAC-0099 attacks", "relation": "uses infection chains via", "object": "booby-trapped ZIP files"}], "entities": [{"entity_id": 0, "entity_name": "Threat Actor UAC-0099", "entity_type": "Attacker", "mentions": []}, {"entity_id": 1, "entity_name": "Ukraine", "entity_type": "Location", "mentions": []}, {"entity_id": 2, "entity_name": "WinRAR vulnerability CVE-2023-38831", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 3, "entity_name": "LONEPAGE", "entity_type": "Malware", "mentions": []}, {"entity_id": 4, "entity_name": "Ukrainian employees working for companies outside of Ukraine", "entity_type": "Organization", "mentions": []}, {"entity_id": 5, "entity_name": "Computer Emergency Response Team of Ukraine (CERT-UA)", "entity_type": "Organization", "mentions": []}, {"entity_id": 6, "entity_name": "June 2023", "entity_type": "Time", "mentions": []}, {"entity_id": 7, "entity_name": "state organizations and media entities", "entity_type": "Organization", "mentions": []}, {"entity_id": 8, "entity_name": "contacting a command-and-control (C2) server", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 9, "entity_name": "keyloggers", "entity_type": "Malware", "mentions": []}, {"entity_id": 10, "entity_name": "stealers", "entity_type": "Malware", "mentions": []}, {"entity_id": 11, "entity_name": "screenshot malware", "entity_type": "Malware", "mentions": []}, {"entity_id": 12, "entity_name": "UAC-0099 attacks", "entity_type": "Event", "mentions": []}, {"entity_id": 13, "entity_name": "HTA attachments", "entity_type": "Indicator:File", "mentions": []}, {"entity_id": 14, "entity_name": "self-extracting (SFX) archives", "entity_type": "Indicator:File", "mentions": []}, {"entity_id": 15, "entity_name": "booby-trapped ZIP files", "entity_type": "Indicator:File", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 12, "entity_text": "UAC-0099 attacks"}, "relation": "are conducted by", "object": {"entity_id": 0, "entity_text": "Threat Actor UAC-0099"}}]}