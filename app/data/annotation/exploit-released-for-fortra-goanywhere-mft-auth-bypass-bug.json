{"text": "Exploit code is now available for a critical authentication bypass vulnerability in Fortra's GoAnywhere MFT (Managed File Transfer) software that allows attackers to create new admin users on unpatched instances via the administration portal. GoAnywhere MFT is a web-based managed file transfer tool that helps organizations transfer files securely with partners and keep audit logs of who accessed all shared files. Today, almost seven weeks later, security researchers with Horizon3's Attack Team published a technical analysis of the vulnerability and shared a proof-of-concept (PoC) exploit that helps create new admin users on vulnerable GoAnywhere MFT instances exposed online. Their exploit takes advantage of the path traversal issue at the root of CVE-2024-0204 to access the vulnerable /InitialAccountSetup.xhtml endpoint and start the initial account setup screen to create a new administrator account.", "explicit_triplets": [{"subject": "Exploit code", "relation": "is available for", "object": "a critical authentication bypass vulnerability in Fortra's GoAnywhere MFT"}, {"subject": "authentication bypass vulnerability in Fortra's GoAnywhere MFT", "relation": "allows", "object": "attackers to create new admin users"}, {"subject": "Horizon3's Attack Team", "relation": "published", "object": "technical analysis of the vulnerability"}, {"subject": "Horizon3's Attack Team", "relation": "shared", "object": "proof-of-concept (PoC) exploit"}, {"subject": "proof-of-concept (PoC) exploit", "relation": "helps create", "object": "new admin users"}, {"subject": "proof-of-concept (PoC) exploit", "relation": "takes advantage of", "object": "path traversal issue at the root of CVE-2024-0204"}, {"subject": "path traversal issue at the root of CVE-2024-0204", "relation": "accesses", "object": "/InitialAccountSetup.xhtml endpoint"}, {"subject": "/InitialAccountSetup.xhtml endpoint", "relation": "starts", "object": "initial account setup screen"}, {"subject": "initial account setup screen", "relation": "allows creation of", "object": "new administrator account"}], "entities": [{"entity_id": 0, "entity_name": "Exploit code", "entity_type": "Malware Characteristic:Payload", "mentions": []}, {"entity_id": 1, "entity_name": "authentication bypass vulnerability in Fortra's GoAnywhere MFT", "entity_type": "Vulnerability", "mentions": ["a critical authentication bypass vulnerability in Fortra's GoAnywhere MFT"]}, {"entity_id": 2, "entity_name": "attackers to create new admin users", "entity_type": "Malware Characteristic:Behavior", "mentions": []}, {"entity_id": 3, "entity_name": "Horizon3's Attack Team", "entity_type": "Attacker", "mentions": []}, {"entity_id": 4, "entity_name": "technical analysis of the vulnerability", "entity_type": "Information", "mentions": []}, {"entity_id": 5, "entity_name": "proof-of-concept (PoC) exploit", "entity_type": "Malware Characteristic:Payload", "mentions": []}, {"entity_id": 6, "entity_name": "new admin users", "entity_type": "Account", "mentions": ["new administrator account"]}, {"entity_id": 7, "entity_name": "path traversal issue at the root of CVE-2024-0204", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 8, "entity_name": "/InitialAccountSetup.xhtml endpoint", "entity_type": "Indicator:URL", "mentions": []}, {"entity_id": 9, "entity_name": "initial account setup screen", "entity_type": "Infrastructure", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 5, "entity_text": "proof-of-concept (PoC) exploit"}, "relation": "exploits", "object": {"entity_id": 1, "entity_text": "authentication bypass vulnerability in Fortra's GoAnywhere MFT"}}]}