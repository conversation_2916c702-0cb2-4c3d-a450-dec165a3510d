{"text": "More threat actors have started exploiting two recently resolved vulnerabilities in the ConnectWise ScreenConnect remote desktop access software. The issues, tracked as CVE-2024-1709 (CVSS score of 10) and CVE-2024-1708 (CVSS score of 8.4), are described as an authentication bypass flaw and a path traversal bug. ConnectWise disclosed the security defects on February 19, when it announced patches for them. Two days later, the company updated its advisory to warn of ongoing exploitation. “Essentially, a bad actor could mimic the role as system admin, delete all other users and take over the instance,” the company notes in its advisory. A proof-of-concept (PoC) exploit targeting the flaws, collectively referred to as SlashAndGrab, was made public last week, and threat actors quickly started exploiting them for malware delivery. Now, Trend Micro says that more cybercrime groups have started exploiting the flaws, including the Black Basta and Bl00dy ransomware groups. Following initial access to vulnerable servers, Black Basta was seen performing reconnaissance, discovery, and elevation of privilege activities, and deploying Cobalt Strike payloads. In addition to searching for members of the ‘domain admin’ group, the attackers also added new accounts to the administrator group and deployed scripts to identify machines that recently connected to the Active Directory environment.", "explicit_triplets": [{"subject": "Threat Actor", "relation": "exploiting", "object": "CVE-2024-1709"}, {"subject": "Threat Actor", "relation": "exploiting", "object": "CVE-2024-1708"}, {"subject": "CVE-2024-1709", "relation": "described as", "object": "authentication bypass flaw"}, {"subject": "CVE-2024-1708", "relation": "described as", "object": "path traversal bug"}, {"subject": "ConnectWise", "relation": "disclosed", "object": "CVE-2024-1709"}, {"subject": "ConnectWise", "relation": "disclosed", "object": "CVE-2024-1708"}, {"subject": "ConnectWise", "relation": "announced patches on", "object": "February 19"}, {"subject": "ConnectWise", "relation": "warned of ongoing exploitation on", "object": "February 21"}, {"subject": "Threat Actor", "relation": "exploiting flaws for", "object": "malware delivery"}, {"subject": "Black Basta", "relation": "exploiting", "object": "CVE-2024-1709"}, {"subject": "Black Basta", "relation": "exploiting", "object": "CVE-2024-1708"}, {"subject": "Bl00dy ransomware", "relation": "exploiting", "object": "CVE-2024-1709"}, {"subject": "Bl00dy ransomware", "relation": "exploiting", "object": "CVE-2024-1708"}, {"subject": "Black Basta", "relation": "deployed", "object": "Cobalt Strike payloads"}, {"subject": "Black Basta", "relation": "performed", "object": "reconnaissance, discovery, and elevation of privilege activities"}], "entities": [{"entity_id": 0, "entity_name": "Threat Actor", "entity_type": "Attacker", "mentions": []}, {"entity_id": 1, "entity_name": "CVE-2024-1709", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 2, "entity_name": "CVE-2024-1708", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 3, "entity_name": "authentication bypass flaw", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 4, "entity_name": "path traversal bug", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 5, "entity_name": "ConnectWise", "entity_type": "Organization", "mentions": []}, {"entity_id": 6, "entity_name": "February 19", "entity_type": "Time", "mentions": []}, {"entity_id": 7, "entity_name": "February 21", "entity_type": "Time", "mentions": []}, {"entity_id": 8, "entity_name": "malware delivery", "entity_type": "Malware Characteristic:Payload", "mentions": []}, {"entity_id": 9, "entity_name": "Black Basta", "entity_type": "Attacker", "mentions": []}, {"entity_id": 10, "entity_name": "Bl00dy ransomware", "entity_type": "Malware", "mentions": []}, {"entity_id": 11, "entity_name": "Cobalt Strike payloads", "entity_type": "Malware Characteristic:Payload", "mentions": []}, {"entity_id": 12, "entity_name": "reconnaissance, discovery, and elevation of privilege activities", "entity_type": "Malware Characteristic:Behavior", "mentions": []}], "implicit_triplets": []}