{"text": "Security researchers are documenting a new vulnerability found in the util-linux core utilities package in Linux systems, warnign that it allows attackers to leak user passwords and modify the clipboard. The security defect, tracked as CVE-2024-28085 and dubbed ‘WallEscape’, impacts the ‘wall’ command of util-linux that fails to filter escape sequences from command line arguments. An attacker could embed escape sequences into crafted messages and send them via the ‘wall’ command, allowing them to leak passwords and modify commands, if specific conditions are met. “This allows unprivileged users to put arbitrary text on other users’ terminals, if mesg is set to y and wall is setgid,” said security researcher <PERSON><PERSON><PERSON><PERSON><PERSON>, who is credited with discovering the flaw, said the bug can be exploited to leak user passwords on Ubuntu 22.04 with default configurations, where ‘wall’ is installed with the special setgid permissions and mesg is set to y. Debian Bookworm is also affected, but CentOS and Red Hat products are not. According to <PERSON><PERSON><PERSON>, the flaw can also leak an unknown command on Ubuntu 22.0 “if a system runs a command when commands are not found, with the unknown command as an argument.” In addition, it can be exploited to change the output of any command.", "explicit_triplets": [{"subject": "Security researchers", "relation": "are documenting", "object": "a new vulnerability"}, {"subject": "a new vulnerability", "relation": "found in", "object": "Linux systems"}, {"subject": "a new vulnerability", "relation": "tracked as", "object": "CVE-2024-28085"}, {"subject": "CVE-2024-28085", "relation": "dubbed", "object": "WallEscape"}, {"subject": "CVE-2024-28085", "relation": "impacts", "object": "util-linux"}, {"subject": "CVE-2024-28085", "relation": "allows attackers to", "object": "leak user passwords"}, {"subject": "CVE-2024-28085", "relation": "allows attackers to", "object": "modify the clipboard"}, {"subject": "<PERSON><PERSON>", "relation": "credited with discovering", "object": "CVE-2024-28085"}, {"subject": "CVE-2024-28085", "relation": "exploitable on", "object": "Ubuntu 22.04"}, {"subject": "CVE-2024-28085", "relation": "affects", "object": "Debian Bookworm"}, {"subject": "CVE-2024-28085", "relation": "does not affect", "object": "CentOS"}, {"subject": "CVE-2024-28085", "relation": "does not affect", "object": "Red Hat products"}], "entities": [{"entity_id": 0, "entity_name": "Security researchers", "entity_type": "Organization", "mentions": []}, {"entity_id": 1, "entity_name": "a new vulnerability", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 2, "entity_name": "Linux systems", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 3, "entity_name": "CVE-2024-28085", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 4, "entity_name": "WallEscape", "entity_type": "Malware", "mentions": []}, {"entity_id": 5, "entity_name": "util-linux", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 6, "entity_name": "leak user passwords", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 7, "entity_name": "modify the clipboard", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 8, "entity_name": "<PERSON><PERSON>", "entity_type": "Attacker", "mentions": []}, {"entity_id": 9, "entity_name": "Ubuntu 22.04", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 10, "entity_name": "Debian Bookworm", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 11, "entity_name": "CentOS", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 12, "entity_name": "Red Hat products", "entity_type": "Infrastructure", "mentions": []}], "implicit_triplets": []}