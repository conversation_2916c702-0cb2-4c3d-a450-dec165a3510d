{"text": "Clop is an extortion and ransomware operation run by a cyber-crime group known as Snakefly (aka TA505, FIN11). While the group initially extorted victims by encrypting files using its own ransomware payload (Ransom.Clop), in recent times it has been known to eschew encryption entirely and rely on the threat of leaking stolen data to extort its victims. The group has a track record in exploiting zero-day vulnerabilities. In 2021, it was linked to the exploitation of multiple vulnerabilities in Accellion FTA, another file-transfer application. Earlier this year it was responsible for exploiting a zero-day vulnerability (CVE-2023-0669) in the GoAnywhere MFT platform.", "explicit_triplets": [{"subject": "<PERSON><PERSON>", "relation": "is operated by", "object": "Snakefly"}, {"subject": "Snakefly", "relation": "is also known as", "object": "TA505"}, {"subject": "Snakefly", "relation": "is also known as", "object": "FIN11"}, {"subject": "<PERSON><PERSON>", "relation": "initially extorted victims by", "object": "encrypting files using its own ransomware payload"}, {"subject": "<PERSON><PERSON><PERSON><PERSON>", "relation": "is a type of", "object": "Malware"}, {"subject": "<PERSON><PERSON>", "relation": "has recently relied on", "object": "leaking stolen data to extort victims"}, {"subject": "<PERSON><PERSON>", "relation": "exploited", "object": "multiple vulnerabilities in Accellion FTA"}, {"subject": "<PERSON><PERSON>", "relation": "exploited", "object": "a zero-day vulnerability (CVE-2023-0669)"}, {"subject": "a zero-day vulnerability (CVE-2023-0669)", "relation": "affects", "object": "GoAnywhere MFT platform"}, {"subject": "Snakefly", "relation": "has a track record in exploiting", "object": "zero-day vulnerabilities"}, {"subject": "multiple vulnerabilities in Accellion FTA", "relation": "were exploited in", "object": "2021"}, {"subject": "a zero-day vulnerability (CVE-2023-0669)", "relation": "was exploited in", "object": "earlier this year"}], "entities": [{"entity_id": 0, "entity_name": "<PERSON><PERSON>", "entity_type": "Malware", "mentions": []}, {"entity_id": 1, "entity_name": "Snakefly", "entity_type": "Attacker", "mentions": []}, {"entity_id": 2, "entity_name": "TA505", "entity_type": "Attacker", "mentions": []}, {"entity_id": 3, "entity_name": "FIN11", "entity_type": "Attacker", "mentions": []}, {"entity_id": 4, "entity_name": "encrypting files using its own ransomware payload", "entity_type": "Malware Characteristic:Behavior", "mentions": []}, {"entity_id": 5, "entity_name": "<PERSON><PERSON><PERSON><PERSON>", "entity_type": "Malware", "mentions": []}, {"entity_id": 6, "entity_name": "Malware", "entity_type": "Malware", "mentions": []}, {"entity_id": 7, "entity_name": "leaking stolen data to extort victims", "entity_type": "Malware Characteristic:Behavior", "mentions": []}, {"entity_id": 8, "entity_name": "multiple vulnerabilities in Accellion FTA", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 9, "entity_name": "a zero-day vulnerability (CVE-2023-0669)", "entity_type": "Vulnerability", "mentions": ["zero-day vulnerabilities"]}, {"entity_id": 10, "entity_name": "GoAnywhere MFT platform", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 11, "entity_name": "2021", "entity_type": "Time", "mentions": []}, {"entity_id": 12, "entity_name": "earlier this year", "entity_type": "Time", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 5, "entity_text": "<PERSON><PERSON><PERSON><PERSON>"}, "relation": "is the ransomware payload used by", "object": {"entity_id": 0, "entity_text": "<PERSON><PERSON>"}}]}