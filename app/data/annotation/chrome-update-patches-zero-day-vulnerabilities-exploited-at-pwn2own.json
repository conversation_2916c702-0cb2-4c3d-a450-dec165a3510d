{"text": "The most severe of the externally reported flaws is a use-after-free bug in ANGLE, the open source cross-platform graphics engine used in Chrome and other popular browsers. The issue is tracked as CVE-2024-2883 and Google notes in its advisory that the researcher who discovered the bug has received a $10,000 bug bounty reward. According to the advisory, the three other defects reported by external researchers are all high-severity vulnerabilities. The first is CVE-2024-2885, a use-after-free issue in Dawn. The remaining two flaws, tracked as CVE-2024-2886 and CVE-2024-2887, are zero-day vulnerabilities that were exploited and reported last week at the Pwn2Own Vancouver 2024 hacking contest. No additional bounty rewards, aside from those earned at the competition, were handed out for these issues. CVE-2024-2886, a use-after-free in WebCodecs, was demonstrated by <PERSON><PERSON><PERSON><PERSON> of KAIST Hacking Lab, who exploited two such issues in the browser at the hacking contest and earned a total of $145,000 in rewards. CVE-2024-2887 is a Type Confusion bug in WebAssembly, exploited on the first day of Pwn2Own by security researcher <PERSON>, who earned a $42,500 reward for it.<PERSON> also demonstrated Safari and Firefox vulnerabilities at the hacking contest, earning a total of over $200,000 in rewards and winning the competition. Mo<PERSON> was first to release patches for the zero-day demonstrated at Pwn2Own.", "explicit_triplets": [{"subject": "use-after-free bug in ANGLE", "relation": "is tracked as", "object": "CVE-2024-2883"}, {"subject": "CVE-2024-2883", "relation": "was discovered by", "object": "researcher"}, {"subject": "researcher", "relation": "received", "object": "$10,000 bug bounty reward"}, {"subject": "use-after-free issue in Dawn", "relation": "is tracked as", "object": "CVE-2024-2885"}, {"subject": "zero-day vulnerabilities", "relation": "are tracked as", "object": "CVE-2024-2886 and CVE-2024-2887"}, {"subject": "CVE-2024-2886", "relation": "is a", "object": "use-after-free in WebCodecs"}, {"subject": "CVE-2024-2886", "relation": "was exploited by", "object": "<PERSON><PERSON><PERSON><PERSON>"}, {"subject": "<PERSON><PERSON><PERSON><PERSON>", "relation": "is affiliated with", "object": "KAIST Hacking Lab"}, {"subject": "<PERSON><PERSON><PERSON><PERSON>", "relation": "earned", "object": "$145,000 reward"}, {"subject": "CVE-2024-2887", "relation": "is a", "object": "Type Confusion bug in WebAssembly"}, {"subject": "CVE-2024-2887", "relation": "was exploited by", "object": "<PERSON>"}, {"subject": "<PERSON>", "relation": "earned", "object": "$42,500 reward"}, {"subject": "<PERSON>", "relation": "demonstrated vulnerabilities on", "object": "Safari and Firefox"}, {"subject": "Mozilla", "relation": "released patches for", "object": "zero-day vulnerabilities"}], "entities": [{"entity_id": 0, "entity_name": "use-after-free bug in ANGLE", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 1, "entity_name": "CVE-2024-2883", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 2, "entity_name": "researcher", "entity_type": "Attacker", "mentions": []}, {"entity_id": 3, "entity_name": "$10,000 bug bounty reward", "entity_type": "Information", "mentions": []}, {"entity_id": 4, "entity_name": "use-after-free issue in Dawn", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 5, "entity_name": "CVE-2024-2885", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 6, "entity_name": "zero-day vulnerabilities", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 7, "entity_name": "CVE-2024-2886 and CVE-2024-2887", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 8, "entity_name": "CVE-2024-2886", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 9, "entity_name": "use-after-free in WebCodecs", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 10, "entity_name": "<PERSON><PERSON><PERSON><PERSON>", "entity_type": "Attacker", "mentions": []}, {"entity_id": 11, "entity_name": "KAIST Hacking Lab", "entity_type": "Organization", "mentions": []}, {"entity_id": 12, "entity_name": "$145,000 reward", "entity_type": "Information", "mentions": []}, {"entity_id": 13, "entity_name": "CVE-2024-2887", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 14, "entity_name": "Type Confusion bug in WebAssembly", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 15, "entity_name": "<PERSON>", "entity_type": "Attacker", "mentions": []}, {"entity_id": 16, "entity_name": "$42,500 reward", "entity_type": "Information", "mentions": []}, {"entity_id": 17, "entity_name": "Safari and Firefox", "entity_type": "Tool", "mentions": []}, {"entity_id": 18, "entity_name": "Mozilla", "entity_type": "Organization", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 10, "entity_text": "<PERSON><PERSON><PERSON><PERSON>"}, "relation": "is not associated with", "object": {"entity_id": 1, "entity_text": "CVE-2024-2883"}}, {"subject": {"entity_id": 10, "entity_text": "<PERSON><PERSON><PERSON><PERSON>"}, "relation": "is not associated with", "object": {"entity_id": 4, "entity_text": "use-after-free issue in Dawn"}}, {"subject": {"entity_id": 6, "entity_text": "zero-day vulnerabilities"}, "relation": "were exploited by", "object": {"entity_id": 10, "entity_text": "<PERSON><PERSON><PERSON><PERSON>"}}, {"subject": {"entity_id": 15, "entity_text": "<PERSON>"}, "relation": "participated in the same competition as", "object": {"entity_id": 10, "entity_text": "<PERSON><PERSON><PERSON><PERSON>"}}]}