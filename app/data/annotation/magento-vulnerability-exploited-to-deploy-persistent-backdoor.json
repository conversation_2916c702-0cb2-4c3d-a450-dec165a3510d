{"text": "Threat actors are exploiting a critical vulnerability in Magento to inject a persistent backdoor into ecommerce websites, cybersecurity firm Sansec reports. The issue, tracked as CVE-2024-20720 (CVSS score of 9.1), is described as an OS command injection flaw leading to arbitrary code execution without user interaction. Adobe resolved the critical vulnerability in February 2024 in both Adobe Commerce and Magento, as part of its Tuesday Patch updates. However, it appears that there are websites that have yet to be updated and remain vulnerable to exploitation. According to Sansec, threat actors have discovered a clever way to exploit CVE-2024-20720, using a crafted layout template in the database to inject XML code that can reinfect Magento servers even after a manual fix. Attackers combine the Magento layout parser with the beberlei/assert package (installed by default) to execute system commands. Because the layout block is tied to the checkout cart, this command is executed whenever <store>/checkout/cart is requested. As part of the observed attacks, the backdoor is added to the automatically generated content management system (CMS) controller, ensuring the backdoor is periodically reinjected and providing persistent remote code execution via POST commands. The threat actors have employed the mechanism to inject a fake Stripe payment skimmer and steal payment data from the compromised web stores.", "explicit_triplets": [{"subject": "Threat actors", "relation": "are exploiting", "object": "CVE-2024-20720"}, {"subject": "CVE-2024-20720", "relation": "affects", "object": "Magento"}, {"subject": "CVE-2024-20720", "relation": "is described as", "object": "an OS command injection flaw"}, {"subject": "Adobe", "relation": "resolved", "object": "CVE-2024-20720"}, {"subject": "Adobe", "relation": "patched", "object": "Magento"}, {"subject": "Threat actors", "relation": "inject", "object": "persistent backdoor into ecommerce websites"}, {"subject": "Threat actors", "relation": "use", "object": "crafted layout template"}, {"subject": "crafted layout template", "relation": "injects", "object": "XML code"}, {"subject": "XML code", "relation": "is used to reinfect", "object": "Magento servers"}, {"subject": "Attackers", "relation": "combine", "object": "Magento layout parser and beberlei/assert package"}, {"subject": "backdoor", "relation": "provides", "object": "persistent remote code execution"}, {"subject": "Threat actors", "relation": "inject", "object": "fake Stripe payment skimmer"}, {"subject": "fake Stripe payment skimmer", "relation": "steals", "object": "payment data"}], "entities": [{"entity_id": 0, "entity_name": "Threat actors", "entity_type": "Attacker", "mentions": []}, {"entity_id": 1, "entity_name": "CVE-2024-20720", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 2, "entity_name": "Magento", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 3, "entity_name": "an OS command injection flaw", "entity_type": "Malware Characteristic:Behavior", "mentions": []}, {"entity_id": 4, "entity_name": "Adobe", "entity_type": "Organization", "mentions": []}, {"entity_id": 5, "entity_name": "persistent backdoor into ecommerce websites", "entity_type": "Malware", "mentions": []}, {"entity_id": 6, "entity_name": "crafted layout template", "entity_type": "Tool", "mentions": []}, {"entity_id": 7, "entity_name": "XML code", "entity_type": "Malware", "mentions": []}, {"entity_id": 8, "entity_name": "Magento servers", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 9, "entity_name": "Attackers", "entity_type": "Attacker", "mentions": []}, {"entity_id": 10, "entity_name": "Magento layout parser and beberlei/assert package", "entity_type": "Tool", "mentions": []}, {"entity_id": 11, "entity_name": "backdoor", "entity_type": "Malware", "mentions": []}, {"entity_id": 12, "entity_name": "persistent remote code execution", "entity_type": "Malware Characteristic:Capability", "mentions": []}, {"entity_id": 13, "entity_name": "fake Stripe payment skimmer", "entity_type": "Malware", "mentions": []}, {"entity_id": 14, "entity_name": "payment data", "entity_type": "Information", "mentions": []}], "implicit_triplets": [{"subject": {"entity_id": 9, "entity_text": "Attackers"}, "relation": "are exploiting", "object": {"entity_id": 1, "entity_text": "CVE-2024-20720"}}, {"subject": {"entity_id": 11, "entity_text": "backdoor"}, "relation": "is deployed by exploiting", "object": {"entity_id": 1, "entity_text": "CVE-2024-20720"}}]}