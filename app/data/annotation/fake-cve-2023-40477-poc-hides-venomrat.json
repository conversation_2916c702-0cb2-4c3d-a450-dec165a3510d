{"text": "Researchers should be aware of threat actors repurposing older proof of concept (PoC) code to quickly craft a fake PoC for a newly released vulnerability. On Aug. 17, 2023, the Zero Day Initiative publicly reported a remote code execution (RCE) vulnerability in WinRAR tracked as CVE-2023-40477. They had disclosed it to the vendor on June 8, 2023. Four days after the public reporting of CVE-2023-40477, an actor using an alias of <PERSON><PERSON>p<PERSON><PERSON> committed a fake PoC script to their GitHub repository. The fake PoC meant to exploit this WinRAR vulnerability was based on a publicly available PoC script that exploited a SQL injection vulnerability in an application called GeoServer, which is tracked as CVE-2023-25157. We analyzed the fake PoC script and all the links in the infection chain, which ultimately installed a VenomRAT payload.", "explicit_triplets": [{"subject": "Zero Day Initiative", "relation": "publicly reported", "object": "CVE-2023-40477"}, {"subject": "CVE-2023-40477", "relation": "is a remote code execution vulnerability in", "object": "WinRAR"}, {"subject": "Zero Day Initiative", "relation": "disclosed CVE-2023-40477 to vendor on", "object": "June 8, 2023"}, {"subject": "whalersplonk", "relation": "committed", "object": "fake PoC script"}, {"subject": "fake PoC script", "relation": "was intended to exploit", "object": "CVE-2023-40477"}, {"subject": "fake PoC script", "relation": "based on", "object": "publicly available PoC script"}, {"subject": "publicly available PoC script", "relation": "exploited", "object": "CVE-2023-25157"}, {"subject": "CVE-2023-25157", "relation": "is a SQL injection vulnerability in", "object": "GeoServer"}, {"subject": "fake PoC script", "relation": "installed", "object": "VenomRAT payload"}], "entities": [{"entity_id": 0, "entity_name": "Zero Day Initiative", "entity_type": "Organization", "mentions": []}, {"entity_id": 1, "entity_name": "CVE-2023-40477", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 2, "entity_name": "WinRAR", "entity_type": "Tool", "mentions": []}, {"entity_id": 3, "entity_name": "June 8, 2023", "entity_type": "Time", "mentions": []}, {"entity_id": 4, "entity_name": "whalersplonk", "entity_type": "Attacker", "mentions": []}, {"entity_id": 5, "entity_name": "fake PoC script", "entity_type": "Malware", "mentions": []}, {"entity_id": 6, "entity_name": "publicly available PoC script", "entity_type": "Exploit Target", "mentions": []}, {"entity_id": 7, "entity_name": "CVE-2023-25157", "entity_type": "Vulnerability", "mentions": []}, {"entity_id": 8, "entity_name": "GeoServer", "entity_type": "Infrastructure", "mentions": []}, {"entity_id": 9, "entity_name": "VenomRAT payload", "entity_type": "Malware", "mentions": []}], "implicit_triplets": []}