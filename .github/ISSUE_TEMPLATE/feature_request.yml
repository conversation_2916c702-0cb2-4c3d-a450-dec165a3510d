name: Feature Request
description: Suggest an idea or enhancement for CTINexus
labels: ["enhancement"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for suggesting a new feature! We appreciate your ideas for improving CTINexus.

  - type: textarea
    id: problem-description
    attributes:
      label: Problem Statement
      description: Is your feature request related to a problem? Please describe the problem you're trying to solve.
      placeholder: A clear and concise description of what the problem is.
    validations:
      required: true

  - type: textarea
    id: proposed-solution
    attributes:
      label: Proposed Solution
      description: Describe the solution you'd like to see
      placeholder: A clear and concise description of what you want to happen.
    validations:
      required: true

  - type: dropdown
    id: feature-priority
    attributes:
      label: Priority Level
      description: How important is this feature to you?
      options:
        - Low - Nice to have
        - Medium - Would be helpful
        - High - Important for my use case
        - Critical - Blocking my work
      default: 1
    validations:
      required: true

  - type: textarea
    id: use-cases
    attributes:
      label: Use Cases
      description: Describe specific use cases or scenarios where this feature would be beneficial
      placeholder: |
        1. When analyzing threat reports from [specific source]...
        2. For researchers working with [specific type of data]...
        3. In enterprise environments where [specific requirement]...
    validations:
      required: false

  - type: textarea
    id: alternatives-considered
    attributes:
      label: Alternatives Considered
      description: Describe any alternative solutions or features you've considered
      placeholder: A clear and concise description of any alternative solutions or features you've considered.
    validations:
      required: false

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Add any other context, links to related research, or relevant information about the feature request
      placeholder: Links to papers, standards, or other relevant information
    validations:
      required: false
