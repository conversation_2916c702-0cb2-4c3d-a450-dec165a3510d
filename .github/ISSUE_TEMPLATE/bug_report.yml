name: Bug Report
description: File a bug report to help us improve CTINexus
labels: ["bug"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report! Please provide as much detail as possible to help us resolve the issue.

  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: A clear and concise description of what the bug is.
      placeholder: Tell us what happened!
    validations:
      required: true

  - type: textarea
    id: steps-to-reproduce
    attributes:
      label: Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
    validations:
      required: true

  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen.
      placeholder: What should have happened instead?
    validations:
      required: true

  - type: dropdown
    id: setup-type
    attributes:
      label: Setup Type
      description: How are you running CTINexus?
      options:
        - Local Python installation
        - Docker
      default: 0
    validations:
      required: true

  - type: dropdown
    id: ai-provider
    attributes:
      label: AI Provider
      description: Which AI provider were you using when the bug occurred?
      options:
        - OpenAI
        - Gemini
        - AWS (Bedrock)
        - Multiple providers
        - Not applicable
      default: 0
    validations:
      required: false

  - type: textarea
    id: environment
    attributes:
      label: Environment Details
      description: Please provide your environment information
      placeholder: |
        - OS: [e.g. macOS 14.0, Ubuntu 22.04, Windows 11]
        - Python version: [e.g. 3.11.5]
        - Browser (if using web interface): [e.g. Chrome, Safari]
    validations:
      required: false

  - type: textarea
    id: error-logs
    attributes:
      label: Error Messages/Logs
      description: If applicable, add any error messages, stack traces, or relevant log output
      render: shell
    validations:
      required: false

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Add any other context about the problem here
      placeholder: Any additional information that might be helpful
    validations:
      required: false
