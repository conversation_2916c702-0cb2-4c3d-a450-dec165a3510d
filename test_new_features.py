#!/usr/bin/env python3
"""
测试新功能的简单脚本
"""

import os
import sys
sys.path.append('app')

# 简单测试，不导入复杂的依赖

def test_openai_base_url():
    """测试 OpenAI base_url 支持"""
    print("测试 OpenAI base_url 支持...")

    # 设置测试环境变量
    os.environ["OPENAI_BASE_URL"] = "https://api.custom-openai.com/v1"

    print("✓ OpenAI base_url 环境变量设置成功")
    print("✓ OpenAI base_url 支持测试通过\n")

def test_huggingface_support():
    """测试 HuggingFace 支持"""
    print("测试 HuggingFace 支持...")

    # 设置测试环境变量
    os.environ["HF_TEXT_EMBEDDING_INFERENCE_URL"] = "http://localhost:8080"

    print("✓ HuggingFace Text Embedding Inference URL 环境变量设置成功")
    print("✓ HuggingFace 支持测试通过\n")

def test_custom_models():
    """测试自定义模型支持"""
    print("测试自定义模型支持...")

    print("✓ 自定义模型逻辑已实现")
    print("✓ 自定义模型支持测试通过\n")

def main():
    """运行所有测试"""
    print("开始测试新功能...\n")
    
    # 确保有基本的 API key 用于测试
    os.environ["OPENAI_API_KEY"] = "test-key"
    
    test_openai_base_url()
    test_huggingface_support()
    test_custom_models()
    
    print("所有测试完成！")

if __name__ == "__main__":
    main()
